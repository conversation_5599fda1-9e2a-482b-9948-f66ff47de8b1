# Database Configuration
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/ml_platform
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=ml_platform

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=

# MinIO Configuration
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET_NAME=ml-platform-data

# MLflow Configuration
MLFLOW_TRACKING_URI=http://localhost:5000
MLFLOW_BACKEND_STORE_URI=postgresql://mlflow:mlflow_password@localhost:5432/mlflow
MLFLOW_DEFAULT_ARTIFACT_ROOT=s3://mlflow-artifacts/



# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=ML Experiment Platform
PROJECT_VERSION=1.0.0
DEBUG=true

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-change-this-in-production

# Email Configuration (Optional)
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=
SMTP_USER=
SMTP_PASSWORD=
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=ML Platform

# Monitoring Configuration
PROMETHEUS_ENABLED=false
GRAFANA_ENABLED=false
ELK_ENABLED=false

# Security Configuration
CORS_ORIGINS=["http://localhost:3000"]
RATE_LIMIT_PER_MINUTE=60

# AI Agents Configuration
AGENTS_ENABLED=true
AGENTS_MAX_CONCURRENT_TASKS=5
AGENTS_TASK_TIMEOUT_MINUTES=60

# Development Configuration
ENVIRONMENT=development
LOG_LEVEL=INFO
