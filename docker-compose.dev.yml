# Docker Compose for ML Platform Development

services:
  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    container_name: ml-platform-postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: ml_platform
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./infrastructure/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: ml-platform-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # MinIO Object Storage
  minio:
    image: minio/minio:latest
    container_name: ml-platform-minio
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # MLflow Server
  mlflow:
    image: ghcr.io/mlflow/mlflow:v2.19.0
    container_name: ml-platform-mlflow
    environment:
      - MLFLOW_BACKEND_STORE_URI=postgresql+psycopg2://postgres:postgres@ml-platform-postgres:5432/mlflow
      - MLFLOW_DEFAULT_ARTIFACT_ROOT=s3://mlflow-artifacts/
      - AWS_ACCESS_KEY_ID=minioadmin
      - AWS_SECRET_ACCESS_KEY=minioadmin
      - MLFLOW_S3_ENDPOINT_URL=http://ml-platform-minio:9000
    ports:
      - "5000:5000"
    command: >
      mlflow server
      --backend-store-uri postgresql+psycopg2://postgres:postgres@ml-platform-postgres:5432/mlflow
      --default-artifact-root s3://mlflow-artifacts/
      --host 0.0.0.0
      --port 5000
    depends_on:
      postgres:
        condition: service_healthy
      minio:
        condition: service_healthy

  # Backend API (Development)
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: ml-platform-api
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - /app/__pycache__
    environment:
      - DATABASE_URL=********************************************************/ml_platform
      - REDIS_URL=redis://ml-platform-redis:6379/0
      - MLFLOW_TRACKING_URI=http://ml-platform-mlflow:5000
      - MINIO_ENDPOINT=ml-platform-minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - JWT_SECRET_KEY=dev-secret-key
      - DEBUG=true
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      mlflow:
        condition: service_started

  # Frontend (Development)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: ml-platform-frontend
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=dev-nextauth-secret
    depends_on:
      - api

  # AI Agents (Development)
  agents:
    build:
      context: ./agents
      dockerfile: Dockerfile.dev
    container_name: ml-platform-agents
    ports:
      - "8001:8001"
    volumes:
      - ./agents:/app
    environment:
      - PLATFORM_API_URL=http://ml-platform-api:8000
      - REDIS_URL=redis://ml-platform-redis:6379/1
      - MLFLOW_TRACKING_URI=http://ml-platform-mlflow:5000
    depends_on:
      - api
      - redis

volumes:
  postgres_data:
  redis_data:
  minio_data:
