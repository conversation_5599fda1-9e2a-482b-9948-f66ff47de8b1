# ML Experiment Platform - Makefile

.PHONY: help install install-dev setup clean test test-backend test-frontend lint format check build up down logs shell

# Default target
help:
	@echo "ML Experiment Platform - Available commands:"
	@echo ""
	@echo "Setup and Installation:"
	@echo "  install       Install production dependencies"
	@echo "  install-dev   Install development dependencies"
	@echo "  setup         Complete development setup"
	@echo ""
	@echo "Development:"
	@echo "  up            Start development environment"
	@echo "  down          Stop development environment"
	@echo "  logs          View logs from all services"
	@echo "  shell         Open shell in backend container"
	@echo ""
	@echo "Code Quality:"
	@echo "  lint          Run linting on all code"
	@echo "  format        Format all code"
	@echo "  check         Run all checks (lint, type check, tests)"
	@echo ""
	@echo "Testing:"
	@echo "  test          Run all tests"
	@echo "  test-backend  Run backend tests only"
	@echo "  test-frontend Run frontend tests only"
	@echo ""
	@echo "Build and Deploy:"
	@echo "  build         Build all Docker images"
	@echo "  clean         Clean up containers, images, and volumes"

# Installation
install:
	@echo "Installing production dependencies..."
	cd backend && pip install -r requirements.txt
	cd frontend && npm install --production

install-dev:
	@echo "Installing development dependencies..."
	cd backend && pip install -r requirements.txt -r requirements-dev.txt
	cd frontend && npm install

setup: install-dev
	@echo "Setting up development environment..."
	pre-commit install
	cp .env.example .env
	@echo "Development environment setup complete!"
	@echo "Please review and update .env file as needed."

# Development
up:
	@echo "Starting development environment..."
	./scripts/dev-setup.sh

down:
	@echo "Stopping development environment..."
	docker compose -f docker-compose.dev.yml down

logs:
	@echo "Viewing logs from all services..."
	docker compose -f docker-compose.dev.yml logs -f

shell:
	@echo "Opening shell in backend container..."
	docker compose -f docker-compose.dev.yml exec api bash

# Code Quality
lint:
	@echo "Running linting..."
	cd backend && flake8 app/
	cd frontend && npm run lint

format:
	@echo "Formatting code..."
	cd backend && black app/ && isort app/
	cd frontend && npm run format

check: lint
	@echo "Running type checks..."
	cd backend && mypy app/
	cd frontend && npm run type-check

# Testing
test:
	@echo "Running all tests..."
	./scripts/run-tests.sh

test-backend:
	@echo "Running backend tests..."
	./scripts/run-tests.sh --backend-only

test-frontend:
	@echo "Running frontend tests..."
	./scripts/run-tests.sh --frontend-only

# Build and Deploy
build:
	@echo "Building Docker images..."
	docker compose -f docker-compose.dev.yml build

clean:
	@echo "Cleaning up..."
	./scripts/cleanup.sh --full --remove-data

# Database operations
migrate:
	@echo "Running database migrations..."
	cd backend && alembic upgrade head

migrate-create:
	@echo "Creating new migration..."
	@read -p "Enter migration message: " msg; \
	cd backend && alembic revision --autogenerate -m "$$msg"

# Utility commands
reset-db:
	@echo "Resetting database..."
	docker compose -f docker-compose.dev.yml down -v
	docker compose -f docker-compose.dev.yml up -d postgres
	sleep 10
	$(MAKE) migrate

docs:
	@echo "Building documentation..."
	cd backend && mkdocs build

docs-serve:
	@echo "Serving documentation..."
	cd backend && mkdocs serve

# Production commands
prod-build:
	@echo "Building production images..."
	docker compose -f docker-compose.prod.yml build

prod-up:
	@echo "Starting production environment..."
	docker compose -f docker-compose.prod.yml up -d

prod-down:
	@echo "Stopping production environment..."
	docker compose -f docker-compose.prod.yml down

prod-logs:
	@echo "Viewing production logs..."
	docker compose -f docker-compose.prod.yml logs -f
