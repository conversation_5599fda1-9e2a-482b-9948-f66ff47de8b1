# ML Experiment Platform - Software Specification Document

## Table of Contents
1. [Executive Summary and System Overview](#1-executive-summary-and-system-overview)
2. [System Architecture](#2-system-architecture)
3. [Technology Stack Details](#3-technology-stack-details)
4. [Component Specifications](#4-component-specifications)
5. [Database Schema Design](#5-database-schema-design)
6. [API Design and Endpoints](#6-api-design-and-endpoints)
7. [AI Agent Architecture and Specifications](#7-ai-agent-architecture-and-specifications)
8. [MLflow and Neptune.AI Integration](#8-mlflow-and-neptuneai-integration)
9. [Docker Configuration and Deployment](#9-docker-configuration-and-deployment)
10. [Security and Authentication](#10-security-and-authentication)
11. [Development Workflow and CI/CD](#11-development-workflow-and-cicd)
12. [Performance and Scalability Considerations](#12-performance-and-scalability-considerations)
13. [Future Roadmap](#13-future-roadmap)

---

## 1. Executive Summary and System Overview

### 1.1 Project Overview
The ML Experiment Platform is a comprehensive, containerized solution designed to streamline machine learning experimentation workflows for data scientists, ML engineers, and researchers. The platform provides an integrated environment for data management, experiment tracking, model development, and collaborative research.

### 1.2 Target Users
- **Data Scientists**: Primary users conducting experiments and model development
- **ML Engineers**: Users focused on model deployment and production workflows
- **Researchers**: Academic and industry researchers requiring reproducible experiments

### 1.3 Scale and Capacity
- **Concurrent Users**: Up to 5 simultaneous users
- **Dataset Size**: Support for datasets up to 8GB in memory
- **Experiment Volume**: Hundreds of experiments per project
- **Deployment**: Ubuntu server environment

### 1.4 Core Objectives
- Simplify ML experiment lifecycle management
- Provide automated AI assistance for common ML tasks
- Ensure reproducibility and collaboration
- Integrate with industry-standard ML tools (MLflow, Neptune.AI)
- Maintain scalable, containerized architecture

### 1.5 Key Features
- **Data Management**: Upload, summarize, and visualize datasets
- **Experiment Tracking**: Comprehensive logging and comparison of ML experiments
- **Project Management**: Organize experiments into logical project structures
- **AI Agents**: Automated assistance for data preparation, feature engineering, model building, and evaluation
- **Integration**: Seamless connection with MLflow and optional Neptune.AI

---

## 2. System Architecture

### 2.1 Architecture Overview
The platform follows a microservices architecture pattern with containerized services orchestrated through Docker Compose. This design ensures scalability, maintainability, and deployment flexibility.

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Browser]
        API_CLIENT[API Clients]
    end
    
    subgraph "Load Balancer"
        NGINX[Nginx Reverse Proxy]
    end
    
    subgraph "Frontend Services"
        FRONTEND[Node.js Frontend]
    end
    
    subgraph "Backend Services"
        API[Python API Gateway]
        AUTH[Authentication Service]
        DATA[Data Management Service]
        EXP[Experiment Service]
        PROJ[Project Service]
        AI_COORD[AI Agent Coordinator]
    end
    
    subgraph "AI Agent Services"
        AI_DATA[Data Prep Agent]
        AI_FEAT[Feature Engineering Agent]
        AI_MODEL[Model Building Agent]
        AI_EVAL[Evaluation Agent]
    end
    
    subgraph "ML Platform Services"
        MLFLOW[MLflow Server]
        NEPTUNE[Neptune.AI Integration]
    end
    
    subgraph "Data Layer"
        POSTGRES[(PostgreSQL)]
        REDIS[(Redis Cache)]
        MINIO[MinIO Object Storage]
    end
    
    WEB --> NGINX
    API_CLIENT --> NGINX
    NGINX --> FRONTEND
    NGINX --> API
    
    API --> AUTH
    API --> DATA
    API --> EXP
    API --> PROJ
    API --> AI_COORD
    
    AI_COORD --> AI_DATA
    AI_COORD --> AI_FEAT
    AI_COORD --> AI_MODEL
    AI_COORD --> AI_EVAL
    
    EXP --> MLFLOW
    EXP --> NEPTUNE
    
    AUTH --> POSTGRES
    DATA --> POSTGRES
    EXP --> POSTGRES
    PROJ --> POSTGRES
    
    DATA --> MINIO
    EXP --> MINIO
    
    API --> REDIS
    AI_COORD --> REDIS
```

### 2.2 Microservices Design Principles
- **Single Responsibility**: Each service handles a specific domain
- **Loose Coupling**: Services communicate via well-defined APIs
- **High Cohesion**: Related functionality grouped within services
- **Fault Tolerance**: Services designed to handle failures gracefully
- **Scalability**: Individual services can be scaled independently

### 2.3 Communication Patterns
- **Synchronous**: REST APIs for real-time operations
- **Asynchronous**: Message queues for long-running tasks
- **Event-Driven**: Service-to-service notifications for state changes

---

## 3. Technology Stack Details

### 3.1 Frontend Technologies
- **Framework**: Node.js with Next.js 14+
- **UI Library**: React 18+ with TypeScript
- **Styling**: Tailwind CSS for responsive design
- **State Management**: Zustand for client-side state
- **Data Fetching**: TanStack Query (React Query) for server state
- **Charts/Visualization**: Plotly.js, D3.js for data visualization
- **Authentication**: NextAuth.js for session management

### 3.2 Backend Technologies
- **Runtime**: Python 3.11+
- **Framework**: FastAPI for high-performance APIs
- **Authentication**: JWT with PyJWT
- **ORM**: SQLAlchemy 2.0+ with Alembic migrations
- **Validation**: Pydantic v2 for data validation
- **Task Queue**: Celery with Redis broker
- **ML Libraries**: scikit-learn, pandas, numpy, matplotlib, seaborn

### 3.3 Database Technologies
- **Primary Database**: PostgreSQL 16+ for relational data
- **Cache**: Redis 7+ for session storage and task queues
- **Object Storage**: MinIO for file and artifact storage
- **Search**: PostgreSQL full-text search with GIN indexes

### 3.4 ML Platform Integration
- **MLflow**: Version 2.19+ for experiment tracking
- **Neptune.AI**: Optional integration via neptune-mlflow plugin
- **Model Registry**: MLflow Model Registry for version control

### 3.5 Infrastructure Technologies
- **Containerization**: Docker 24+ with multi-stage builds
- **Orchestration**: Docker Compose for local development
- **Reverse Proxy**: Nginx for load balancing and SSL termination
- **Monitoring**: Prometheus + Grafana for metrics
- **Logging**: ELK Stack (Elasticsearch, Logstash, Kibana)

---

## 4. Component Specifications

### 4.1 Frontend Component (Node.js/React)

#### 4.1.1 Architecture
```
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication routes
│   ├── dashboard/         # Main dashboard
│   ├── projects/          # Project management
│   ├── experiments/       # Experiment tracking
│   └── datasets/          # Data management
├── components/            # Reusable UI components
│   ├── ui/               # Base UI components
│   ├── charts/           # Visualization components
│   ├── forms/            # Form components
│   └── layout/           # Layout components
├── lib/                  # Utility libraries
│   ├── api.ts           # API client
│   ├── auth.ts          # Authentication utilities
│   └── utils.ts         # General utilities
├── hooks/               # Custom React hooks
├── stores/              # Zustand stores
└── types/               # TypeScript type definitions
```

#### 4.1.2 Key Features
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Real-time Updates**: WebSocket connections for live experiment updates
- **Data Visualization**: Interactive charts and plots for experiment results
- **File Upload**: Drag-and-drop interface for dataset uploads
- **Collaborative Features**: Real-time collaboration indicators

#### 4.1.3 Performance Optimizations
- **Code Splitting**: Route-based and component-based splitting
- **Image Optimization**: Next.js Image component with lazy loading
- **Caching**: Aggressive caching of API responses
- **Bundle Analysis**: Regular bundle size monitoring

### 4.2 Backend API Component (Python/FastAPI)

#### 4.2.1 Project Structure
```
backend/
├── app/
│   ├── api/                 # API routes
│   │   ├── v1/             # API version 1
│   │   │   ├── auth.py     # Authentication endpoints
│   │   │   ├── projects.py # Project management
│   │   │   ├── experiments.py # Experiment tracking
│   │   │   ├── datasets.py # Data management
│   │   │   └── agents.py   # AI agent endpoints
│   ├── core/               # Core functionality
│   │   ├── config.py       # Configuration management
│   │   ├── security.py     # Security utilities
│   │   └── database.py     # Database connection
│   ├── models/             # SQLAlchemy models
│   ├── schemas/            # Pydantic schemas
│   ├── services/           # Business logic
│   ├── agents/             # AI agent implementations
│   └── utils/              # Utility functions
├── tests/                  # Test suite
├── migrations/             # Database migrations
└── requirements.txt        # Python dependencies
```

#### 4.2.2 API Design Principles
- **RESTful Design**: Standard HTTP methods and status codes
- **OpenAPI Documentation**: Automatic API documentation with FastAPI
- **Validation**: Comprehensive input validation with Pydantic
- **Error Handling**: Consistent error response format
- **Rate Limiting**: API rate limiting to prevent abuse

#### 4.2.3 Performance Features
- **Async/Await**: Asynchronous request handling
- **Connection Pooling**: Database connection pooling
- **Caching**: Redis-based caching for frequently accessed data
- **Background Tasks**: Celery for long-running operations

### 4.3 Database Component (PostgreSQL)

#### 4.3.1 Database Configuration
- **Version**: PostgreSQL 16+
- **Extensions**: 
  - `uuid-ossp` for UUID generation
  - `pg_trgm` for fuzzy text search
  - `btree_gin` for composite indexes
- **Connection Pooling**: PgBouncer for connection management
- **Backup Strategy**: Automated daily backups with point-in-time recovery

#### 4.3.2 Performance Tuning
```sql
-- PostgreSQL configuration optimizations
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
```

### 4.4 AI Agent Components

#### 4.4.1 Agent Coordinator Service
- **Purpose**: Orchestrates AI agent execution and manages agent lifecycle
- **Technology**: Python with FastAPI and Celery
- **Features**:
  - Agent task scheduling and queuing
  - Progress tracking and status updates
  - Resource allocation and management
  - Error handling and retry logic

#### 4.4.2 Individual Agent Services
Each AI agent runs as a separate containerized service:

1. **Data Preparation Agent**
   - Data cleaning and preprocessing
   - Missing value handling
   - Data type conversion and validation
   - Outlier detection and treatment

2. **Feature Engineering Agent**
   - Automated feature selection
   - Feature transformation and scaling
   - Dimensionality reduction
   - Feature importance analysis

3. **Model Building Agent**
   - Algorithm selection and hyperparameter tuning
   - Cross-validation and model training
   - Ensemble methods and model stacking
   - AutoML capabilities

4. **Evaluation Agent**
   - Model performance metrics calculation
   - Statistical significance testing
   - Bias and fairness analysis
   - Model interpretability reports

---

## 5. Database Schema Design

### 5.1 Core Tables

#### 5.1.1 Users and Authentication
```sql
-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    is_superuser BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User sessions for JWT token management
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### 5.1.2 Projects and Organization
```sql
-- Projects table
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    owner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Project members for collaboration
CREATE TABLE project_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    role VARCHAR(50) DEFAULT 'member', -- owner, admin, member, viewer
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(project_id, user_id)
);
```

#### 5.1.3 Datasets and Data Management
```sql
-- Datasets table
CREATE TABLE datasets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    uploaded_by UUID REFERENCES users(id),
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT,
    file_type VARCHAR(50),
    row_count INTEGER,
    column_count INTEGER,
    schema_info JSONB,
    summary_stats JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Dataset columns for detailed schema information
CREATE TABLE dataset_columns (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    dataset_id UUID REFERENCES datasets(id) ON DELETE CASCADE,
    column_name VARCHAR(255) NOT NULL,
    data_type VARCHAR(100),
    is_nullable BOOLEAN DEFAULT true,
    unique_values INTEGER,
    missing_values INTEGER,
    column_stats JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### 5.1.4 Experiments and ML Tracking
```sql
-- Experiments table
CREATE TABLE experiments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    dataset_id UUID REFERENCES datasets(id),
    created_by UUID REFERENCES users(id),
    mlflow_experiment_id VARCHAR(255),
    neptune_run_id VARCHAR(255),
    status VARCHAR(50) DEFAULT 'created', -- created, running, completed, failed
    experiment_type VARCHAR(100), -- supervised_classification, supervised_regression, unsupervised
    target_column VARCHAR(255),
    feature_columns TEXT[],
    parameters JSONB,
    metrics JSONB,
    artifacts JSONB,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Experiment runs for tracking individual model runs
CREATE TABLE experiment_runs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    experiment_id UUID REFERENCES experiments(id) ON DELETE CASCADE,
    run_name VARCHAR(255),
    mlflow_run_id VARCHAR(255) UNIQUE,
    neptune_run_id VARCHAR(255),
    algorithm VARCHAR(100),
    hyperparameters JSONB,
    metrics JSONB,
    artifacts JSONB,
    status VARCHAR(50) DEFAULT 'running',
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE,
    duration_seconds INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### 5.1.5 AI Agent Management
```sql
-- AI agent tasks
CREATE TABLE agent_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_type VARCHAR(100) NOT NULL, -- data_prep, feature_eng, model_build, evaluation
    experiment_id UUID REFERENCES experiments(id) ON DELETE CASCADE,
    dataset_id UUID REFERENCES datasets(id),
    created_by UUID REFERENCES users(id),
    status VARCHAR(50) DEFAULT 'pending', -- pending, running, completed, failed
    input_parameters JSONB,
    output_results JSONB,
    error_message TEXT,
    progress_percentage INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Agent execution logs
CREATE TABLE agent_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID REFERENCES agent_tasks(id) ON DELETE CASCADE,
    log_level VARCHAR(20) DEFAULT 'INFO',
    message TEXT NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 5.2 Indexes and Performance Optimization
```sql
-- Performance indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_projects_owner ON projects(owner_id);
CREATE INDEX idx_datasets_project ON datasets(project_id);
CREATE INDEX idx_experiments_project ON experiments(project_id);
CREATE INDEX idx_experiments_dataset ON experiments(dataset_id);
CREATE INDEX idx_experiment_runs_experiment ON experiment_runs(experiment_id);
CREATE INDEX idx_agent_tasks_experiment ON agent_tasks(experiment_id);
CREATE INDEX idx_agent_tasks_status ON agent_tasks(status);

-- Full-text search indexes
CREATE INDEX idx_projects_search ON projects USING GIN(to_tsvector('english', name || ' ' || COALESCE(description, '')));
CREATE INDEX idx_experiments_search ON experiments USING GIN(to_tsvector('english', name || ' ' || COALESCE(description, '')));

-- JSONB indexes for efficient querying
CREATE INDEX idx_experiments_parameters ON experiments USING GIN(parameters);
CREATE INDEX idx_experiments_metrics ON experiments USING GIN(metrics);
CREATE INDEX idx_experiment_runs_metrics ON experiment_runs USING GIN(metrics);
```

### 5.3 Database Triggers and Functions
```sql
-- Update timestamp trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply trigger to relevant tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_datasets_updated_at BEFORE UPDATE ON datasets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_experiments_updated_at BEFORE UPDATE ON experiments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

---

## 6. API Design and Endpoints

### 6.1 API Architecture Principles
- **RESTful Design**: Following REST conventions for resource-based URLs
- **Versioning**: API versioning through URL path (`/api/v1/`)
- **Consistent Response Format**: Standardized JSON response structure
- **Error Handling**: HTTP status codes with detailed error messages
- **Authentication**: JWT-based authentication for all protected endpoints

### 6.2 Authentication Endpoints

```python
# Authentication API endpoints
@router.post("/auth/register", response_model=UserResponse)
async def register_user(user_data: UserCreate, db: Session = Depends(get_db)):
    """Register a new user account"""
    pass

@router.post("/auth/login", response_model=TokenResponse)
async def login_user(credentials: UserLogin, db: Session = Depends(get_db)):
    """Authenticate user and return JWT tokens"""
    pass

@router.post("/auth/refresh", response_model=TokenResponse)
async def refresh_token(refresh_token: str, db: Session = Depends(get_db)):
    """Refresh access token using refresh token"""
    pass

@router.post("/auth/logout")
async def logout_user(current_user: User = Depends(get_current_user)):
    """Logout user and invalidate tokens"""
    pass
```

### 6.3 Project Management Endpoints

```python
# Project management API endpoints
@router.get("/projects", response_model=List[ProjectResponse])
async def list_projects(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """List all projects accessible to the current user"""
    pass

@router.post("/projects", response_model=ProjectResponse)
async def create_project(
    project_data: ProjectCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new project"""
    pass

@router.get("/projects/{project_id}", response_model=ProjectResponse)
async def get_project(
    project_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get project details by ID"""
    pass

@router.put("/projects/{project_id}", response_model=ProjectResponse)
async def update_project(
    project_id: UUID,
    project_data: ProjectUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update project information"""
    pass

@router.delete("/projects/{project_id}")
async def delete_project(
    project_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a project and all associated data"""
    pass
```

### 6.4 Dataset Management Endpoints

```python
# Dataset management API endpoints
@router.post("/datasets/upload", response_model=DatasetResponse)
async def upload_dataset(
    file: UploadFile = File(...),
    project_id: UUID = Form(...),
    name: str = Form(...),
    description: Optional[str] = Form(None),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Upload a new dataset file"""
    pass

@router.get("/datasets", response_model=List[DatasetResponse])
async def list_datasets(
    project_id: Optional[UUID] = None,
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """List datasets with optional project filtering"""
    pass

@router.get("/datasets/{dataset_id}", response_model=DatasetResponse)
async def get_dataset(
    dataset_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get dataset details and metadata"""
    pass

@router.get("/datasets/{dataset_id}/summary", response_model=DatasetSummaryResponse)
async def get_dataset_summary(
    dataset_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get statistical summary of dataset"""
    pass

@router.get("/datasets/{dataset_id}/preview")
async def preview_dataset(
    dataset_id: UUID,
    rows: int = 100,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Preview first N rows of dataset"""
    pass
```

### 6.5 Experiment Management Endpoints

```python
# Experiment management API endpoints
@router.post("/experiments", response_model=ExperimentResponse)
async def create_experiment(
    experiment_data: ExperimentCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new ML experiment"""
    pass

@router.get("/experiments", response_model=List[ExperimentResponse])
async def list_experiments(
    project_id: Optional[UUID] = None,
    status: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """List experiments with filtering options"""
    pass

@router.get("/experiments/{experiment_id}", response_model=ExperimentResponse)
async def get_experiment(
    experiment_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get experiment details and results"""
    pass

@router.post("/experiments/{experiment_id}/runs", response_model=ExperimentRunResponse)
async def create_experiment_run(
    experiment_id: UUID,
    run_data: ExperimentRunCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new experiment run"""
    pass

@router.get("/experiments/{experiment_id}/runs", response_model=List[ExperimentRunResponse])
async def list_experiment_runs(
    experiment_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """List all runs for an experiment"""
    pass
```

### 6.6 AI Agent Endpoints

```python
# AI Agent API endpoints
@router.post("/agents/data-prep", response_model=AgentTaskResponse)
async def start_data_prep_agent(
    task_data: DataPrepTaskCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Start data preparation agent task"""
    pass

@router.post("/agents/feature-engineering", response_model=AgentTaskResponse)
async def start_feature_engineering_agent(
    task_data: FeatureEngTaskCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Start feature engineering agent task"""
    pass

@router.post("/agents/model-building", response_model=AgentTaskResponse)
async def start_model_building_agent(
    task_data: ModelBuildTaskCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Start model building agent task"""
    pass

@router.post("/agents/evaluation", response_model=AgentTaskResponse)
async def start_evaluation_agent(
    task_data: EvaluationTaskCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Start model evaluation agent task"""
    pass

@router.get("/agents/tasks/{task_id}", response_model=AgentTaskResponse)
async def get_agent_task_status(
    task_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get status and results of agent task"""
    pass

@router.get("/agents/tasks/{task_id}/logs", response_model=List[AgentLogResponse])
async def get_agent_task_logs(
    task_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get execution logs for agent task"""
    pass
```

### 6.7 Response Models and Schemas

```python
# Pydantic response models
class UserResponse(BaseModel):
    id: UUID
    email: str
    username: str
    first_name: Optional[str]
    last_name: Optional[str]
    is_active: bool
    created_at: datetime

class ProjectResponse(BaseModel):
    id: UUID
    name: str
    description: Optional[str]
    owner_id: UUID
    is_public: bool
    created_at: datetime
    updated_at: datetime
    member_count: int
    experiment_count: int

class DatasetResponse(BaseModel):
    id: UUID
    name: str
    description: Optional[str]
    project_id: UUID
    file_size: int
    file_type: str
    row_count: int
    column_count: int
    created_at: datetime
    updated_at: datetime

class ExperimentResponse(BaseModel):
    id: UUID
    name: str
    description: Optional[str]
    project_id: UUID
    dataset_id: UUID
    status: str
    experiment_type: str
    target_column: Optional[str]
    feature_columns: List[str]
    parameters: Dict[str, Any]
    metrics: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    run_count: int

class AgentTaskResponse(BaseModel):
    id: UUID
    task_type: str
    experiment_id: UUID
    status: str
    progress_percentage: int
    input_parameters: Dict[str, Any]
    output_results: Optional[Dict[str, Any]]
    error_message: Optional[str]
    created_at: datetime
    updated_at: datetime
```

---

## 7. AI Agent Architecture and Specifications

### 7.1 AI Agent System Overview

The AI Agent system provides automated assistance for common machine learning tasks, reducing manual effort and improving experiment efficiency. Each agent is designed as a specialized microservice with specific domain expertise.

```mermaid
graph TB
    subgraph "AI Agent Coordinator"
        COORD[Agent Coordinator Service]
        QUEUE[Task Queue Manager]
        MONITOR[Progress Monitor]
    end
    
    subgraph "AI Agents"
        DATA_AGENT[Data Prep Agent]
        FEAT_AGENT[Feature Engineering Agent]
        MODEL_AGENT[Model Building Agent]
        EVAL_AGENT[Evaluation Agent]
    end
    
    subgraph "Shared Resources"
        PLATFORM_API[Platform API]
        KNOWLEDGE[Knowledge Base]
        TEMPLATES[Code Templates]
    end
    
    COORD --> QUEUE
    COORD --> MONITOR
    QUEUE --> DATA_AGENT
    QUEUE --> FEAT_AGENT
    QUEUE --> MODEL_AGENT
    QUEUE --> EVAL_AGENT
    
    DATA_AGENT --> PLATFORM_API
    FEAT_AGENT --> PLATFORM_API
    MODEL_AGENT --> PLATFORM_API
    EVAL_AGENT --> PLATFORM_API
    
    DATA_AGENT --> KNOWLEDGE
    FEAT_AGENT --> KNOWLEDGE
    MODEL_AGENT --> KNOWLEDGE
    EVAL_AGENT --> KNOWLEDGE
```

### 7.2 Agent Coordinator Service

#### 7.2.1 Responsibilities
- **Task Orchestration**: Manages agent task lifecycle and execution order
- **Resource Management**: Allocates computational resources to agents
- **Progress Tracking**: Monitors and reports agent task progress
- **Error Handling**: Manages failures and implements retry logic
- **Communication**: Facilitates communication between agents and platform

#### 7.2.2 Implementation Details
```python
# Agent Coordinator Service
class AgentCoordinator:
    def __init__(self):
        self.task_queue = TaskQueue()
        self.agent_registry = AgentRegistry()
        self.progress_tracker = ProgressTracker()
    
    async def submit_task(self, task: AgentTask) -> str:
        """Submit a new agent task for execution"""
        task_id = await self.task_queue.enqueue(task)
        await self.progress_tracker.initialize_task(task_id)
        return task_id
    
    async def get_task_status(self, task_id: str) -> TaskStatus:
        """Get current status of an agent task"""
        return await self.progress_tracker.get_status(task_id)
    
    async def cancel_task(self, task_id: str) -> bool:
        """Cancel a running agent task"""
        return await self.task_queue.cancel_task(task_id)
```

### 7.3 Data Preparation Agent

#### 7.3.1 Capabilities
- **Data Quality Assessment**: Identify missing values, duplicates, and anomalies
- **Data Cleaning**: Handle missing values, remove duplicates, fix data types
- **Data Validation**: Ensure data integrity and consistency
- **Preprocessing**: Standardize formats and prepare data for analysis

#### 7.3.2 Implementation
```python
class DataPrepAgent:
    def __init__(self):
        self.platform_api = PlatformAPI()
        self.knowledge_base = KnowledgeBase()
    
    async def analyze_data_quality(self, dataset_id: str) -> DataQualityReport:
        """Analyze dataset quality and identify issues"""
        dataset = await self.platform_api.get_dataset(dataset_id)
        df = pd.read_csv(dataset.file_path)
        
        report = DataQualityReport()
        report.missing_values = df.isnull().sum().to_dict()
        report.duplicates = df.duplicated().sum()
        report.data_types = df.dtypes.to_dict()
        report.outliers = self._detect_outliers(df)
        
        return report
    
    async def clean_dataset(self, dataset_id: str, cleaning_config: dict) -> str:
        """Apply data cleaning operations based on configuration"""
        dataset = await self.platform_api.get_dataset(dataset_id)
        df = pd.read_csv(dataset.file_path)
        
        # Apply cleaning operations
        if cleaning_config.get('handle_missing'):
            df = self._handle_missing_values(df, cleaning_config['missing_strategy'])
        
        if cleaning_config.get('remove_duplicates'):
            df = df.drop_duplicates()
        
        if cleaning_config.get('fix_data_types'):
            df = self._fix_data_types(df, cleaning_config['type_mapping'])
        
        # Save cleaned dataset
        cleaned_path = f"{dataset.file_path}_cleaned.csv"
        df.to_csv(cleaned_path, index=False)
        
        return cleaned_path
```

### 7.4 Feature Engineering Agent

#### 7.4.1 Capabilities
- **Feature Selection**: Identify most relevant features for the target variable
- **Feature Transformation**: Apply scaling, encoding, and mathematical transformations
- **Feature Creation**: Generate new features through combinations and interactions
- **Dimensionality Reduction**: Apply PCA, t-SNE, and other reduction techniques

#### 7.4.2 Implementation
```python
class FeatureEngineeringAgent:
    def __init__(self):
        self.platform_api = PlatformAPI()
        self.feature_selectors = {
            'correlation': self._correlation_selection,
            'mutual_info': self._mutual_info_selection,
            'rfe': self._recursive_feature_elimination
        }
    
    async def select_features(self, dataset_id: str, target_column: str, method: str = 'correlation') -> List[str]:
        """Select most relevant features for the target variable"""
        dataset = await self.platform_api.get_dataset(dataset_id)
        df = pd.read_csv(dataset.file_path)
        
        X = df.drop(columns=[target_column])
        y = df[target_column]
        
        selector = self.feature_selectors[method]
        selected_features = selector(X, y)
        
        return selected_features
    
    async def engineer_features(self, dataset_id: str, engineering_config: dict) -> str:
        """Apply feature engineering transformations"""
        dataset = await self.platform_api.get_dataset(dataset_id)
        df = pd.read_csv(dataset.file_path)
        
        # Apply transformations
        if engineering_config.get('scaling'):
            df = self._apply_scaling(df, engineering_config['scaling_method'])
        
        if engineering_config.get('encoding'):
            df = self._apply_encoding(df, engineering_config['categorical_columns'])
        
        if engineering_config.get('polynomial_features'):
            df = self._create_polynomial_features(df, engineering_config['degree'])
        
        # Save engineered dataset
        engineered_path = f"{dataset.file_path}_engineered.csv"
        df.to_csv(engineered_path, index=False)
        
        return engineered_path
```

### 7.5 Model Building Agent

#### 7.5.1 Capabilities
- **Algorithm Selection**: Choose appropriate algorithms based on problem type
- **Hyperparameter Tuning**: Optimize model parameters using grid search or Bayesian optimization
- **Cross-Validation**: Implement robust validation strategies
- **Ensemble Methods**: Combine multiple models for improved performance

#### 7.5.2 Implementation
```python
class ModelBuildingAgent:
    def __init__(self):
        self.platform_api = PlatformAPI()
        self.mlflow_client = MLflowClient()
        self.algorithms = {
            'classification': [
                RandomForestClassifier,
                GradientBoostingClassifier,
                SVC,
                LogisticRegression
            ],
            'regression': [
                RandomForestRegressor,
                GradientBoostingRegressor,
                SVR,
                LinearRegression
            ]
        }
    
    async def build_models(self, experiment_id: str, model_config: dict) -> List[str]:
        """Build and train multiple models for comparison"""
        experiment = await self.platform_api.get_experiment(experiment_id)
        dataset = await self.platform_api.get_dataset(experiment.dataset_id)
        
        df = pd.read_csv(dataset.file_path)
        X = df.drop(columns=[experiment.target_column])
        y = df[experiment.target_column]
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        model_runs = []
        algorithms = self.algorithms[experiment.experiment_type]
        
        for algorithm_class in algorithms:
            with mlflow.start_run(experiment_id=experiment.mlflow_experiment_id):
                # Hyperparameter tuning
                best_params = self._tune_hyperparameters(algorithm_class, X_train, y_train)
                
                # Train model with best parameters
                model = algorithm_class(**best_params)
                model.fit(X_train, y_train)
                
                # Log model and metrics
                mlflow.sklearn.log_model(model, "model")
                mlflow.log_params(best_params)
                
                # Evaluate model
                predictions = model.predict(X_test)
                metrics = self._calculate_metrics(y_test, predictions, experiment.experiment_type)
                mlflow.log_metrics(metrics)
                
                model_runs.append(mlflow.active_run().info.run_id)
        
        return model_runs
```

### 7.6 Evaluation Agent

#### 7.6.1 Capabilities
- **Performance Metrics**: Calculate comprehensive evaluation metrics
- **Statistical Testing**: Perform significance tests for model comparison
- **Bias Analysis**: Detect and analyze model bias across different groups
- **Interpretability**: Generate model explanations and feature importance

#### 7.6.2 Implementation
```python
class EvaluationAgent:
    def __init__(self):
        self.platform_api = PlatformAPI()
        self.mlflow_client = MLflowClient()
    
    async def evaluate_models(self, experiment_id: str, evaluation_config: dict) -> EvaluationReport:
        """Comprehensive evaluation of experiment models"""
        experiment = await self.platform_api.get_experiment(experiment_id)
        runs = await self.platform_api.get_experiment_runs(experiment_id)
        
        evaluation_report = EvaluationReport()
        
        for run in runs:
            model = mlflow.sklearn.load_model(f"runs:/{run.mlflow_run_id}/model")
            
            # Load test data
            dataset = await self.platform_api.get_dataset(experiment.dataset_id)
            df = pd.read_csv(dataset.file_path)
            X_test, y_test = self._prepare_test_data(df, experiment.target_column)
            
            # Calculate metrics
            predictions = model.predict(X_test)
            metrics = self._calculate_comprehensive_metrics(y_test, predictions, experiment.experiment_type)
            
            # Bias analysis
            if evaluation_config.get('bias_analysis'):
                bias_metrics = self._analyze_bias(model, X_test, y_test, evaluation_config['protected_attributes'])
                metrics.update(bias_metrics)
            
            # Feature importance
            if hasattr(model, 'feature_importances_'):
                feature_importance = dict(zip(X_test.columns, model.feature_importances_))
                metrics['feature_importance'] = feature_importance
            
            evaluation_report.add_run_evaluation(run.id, metrics)
        
        # Statistical comparison
        if len(runs) > 1:
            evaluation_report.statistical_comparison = self._compare_models_statistically(runs)
        
        return evaluation_report
```

### 7.7 Agent Knowledge Base

#### 7.7.1 Platform Integration Knowledge
Each agent maintains knowledge about the platform's API and data structures:

```python
class PlatformKnowledge:
    """Knowledge base for platform-specific operations"""
    
    def __init__(self):
        self.api_endpoints = {
            'datasets': '/api/v1/datasets',
            'experiments': '/api/v1/experiments',
            'projects': '/api/v1/projects'
        }
        
        self.data_types = {
            'numerical': ['int64', 'float64'],
            'categorical': ['object', 'category'],
            'datetime': ['datetime64']
        }
        
        self.ml_algorithms = {
            'classification': {
                'binary': ['LogisticRegression', 'RandomForestClassifier', 'SVC'],
                'multiclass': ['RandomForestClassifier', 'GradientBoostingClassifier']
            },
            'regression': ['LinearRegression', 'RandomForestRegressor', 'SVR']
        }
    
    def get_recommended_algorithms(self, problem_type: str, dataset_size: int) -> List[str]:
        """Get algorithm recommendations based on problem type and dataset size"""
        algorithms = self.ml_algorithms.get(problem_type, [])
        
        # Filter based on dataset size
        if dataset_size < 1000:
            # Prefer simpler algorithms for small datasets
            return [alg for alg in algorithms if 'Linear' in alg or 'Logistic' in alg]
        else:
            return algorithms
```

---

## 8. MLflow and Neptune.AI Integration

### 8.1 MLflow Integration Architecture

MLflow serves as the primary experiment tracking platform, providing comprehensive logging, model registry, and deployment capabilities.

#### 8.1.1 MLflow Server Configuration
```yaml
# docker-compose.yml - MLflow service
mlflow:
  image: ghcr.io/mlflow/mlflow:v2.19.0
  container_name: mlflow-server
  ports:
    - "5000:5000"
  environment:
    - MLFLOW_BACKEND_STORE_URI=postgresql+psycopg2://mlflow:${MLFLOW_DB_PASSWORD}@postgres:5432/mlflow
    - MLFLOW_DEFAULT_ARTIFACT_ROOT=s3://mlflow-artifacts/
    - AWS_ACCESS_KEY_ID=${MINIO_ACCESS_KEY}
    - AWS_SECRET_ACCESS_KEY=${MINIO_SECRET_KEY}
    - MLFLOW_S3_ENDPOINT_URL=http://minio:9000
  command: >
    mlflow server
    --backend-store-uri postgresql+psycopg2://mlflow:${MLFLOW_DB_PASSWORD}@postgres:5432/mlflow
    --default-artifact-root s3://mlflow-artifacts/
    --host 0.0.0.0
    --port 5000
  depends_on:
    - postgres
    - minio
  volumes:
    - mlflow-data:/mlflow
  networks:
    - ml-platform-network
```

#### 8.1.2 MLflow Integration Service
```python
class MLflowIntegration:
    def __init__(self, tracking_uri: str):
        self.client = MlflowClient(tracking_uri=tracking_uri)
        mlflow.set_tracking_uri(tracking_uri)
    
    async def create_experiment(self, experiment_name: str, tags: dict = None) -> str:
        """Create a new MLflow experiment"""
        try:
            experiment_id = mlflow.create_experiment(
                name=experiment_name,
                tags=tags or {}
            )
            return experiment_id
        except Exception as e:
            # Handle experiment already exists
            experiment = mlflow.get_experiment_by_name(experiment_name)
            return experiment.experiment_id
    
    async def start_run(self, experiment_id: str, run_name: str = None) -> str:
        """Start a new MLflow run"""
        run = mlflow.start_run(
            experiment_id=experiment_id,
            run_name=run_name
        )
        return run.info.run_id
    
    async def log_parameters(self, params: dict):
        """Log parameters to current run"""
        mlflow.log_params(params)
    
    async def log_metrics(self, metrics: dict, step: int = None):
        """Log metrics to current run"""
        for key, value in metrics.items():
            mlflow.log_metric(key, value, step=step)
    
    async def log_model(self, model, model_name: str, signature=None):
        """Log model to current run"""
        if hasattr(model, 'predict'):
            mlflow.sklearn.log_model(
                sk_model=model,
                artifact_path=model_name,
                signature=signature
            )
    
    async def log_artifact(self, local_path: str, artifact_path: str = None):
        """Log artifact to current run"""
        mlflow.log_artifact(local_path, artifact_path)
    
    async def register_model(self, model_uri: str, model_name: str) -> str:
        """Register model in MLflow Model Registry"""
        result = mlflow.register_model(model_uri, model_name)
        return result.version
```

### 8.2 Neptune.AI Integration (Optional)

Neptune.AI integration provides enhanced collaboration features and advanced experiment management capabilities.

#### 8.2.1 Neptune Integration Setup
```python
# Neptune integration using neptune-mlflow plugin
import neptune
from neptune_mlflow_plugin import create_neptune_tracking_uri

class NeptuneIntegration:
    def __init__(self, api_token: str, project: str):
        self.api_token = api_token
        self.project = project
        self.neptune_uri = None
    
    async def initialize_neptune_tracking(self, tags: list = None) -> str:
        """Initialize Neptune tracking URI for MLflow integration"""
        self.neptune_uri = create_neptune_tracking_uri(
            api_token=self.api_token,
            project=self.project,
            tags=tags or ["mlflow", "platform"]
        )
        
        # Set MLflow tracking URI to Neptune
        mlflow.set_tracking_uri(self.neptune_uri)
        return self.neptune_uri
    
    async def create_neptune_run(self, experiment_name: str, tags: list = None) -> str:
        """Create a new Neptune run"""
        run = neptune.init_run(
            project=self.project,
            api_token=self.api_token,
            name=experiment_name,
            tags=tags or []
        )
        return run["sys/id"].fetch()
    
    async def log_to_neptune(self, run_id: str, data: dict):
        """Log data directly to Neptune"""
        run = neptune.init_run(
            project=self.project,
            api_token=self.api_token,
            with_id=run_id
        )
        
        for key, value in data.items():
            run[key] = value
        
        run.stop()
```

#### 8.2.2 Dual Tracking Implementation
```python
class DualTrackingService:
    """Service to handle both MLflow and Neptune tracking simultaneously"""
    
    def __init__(self, mlflow_uri: str, neptune_config: dict = None):
        self.mlflow = MLflowIntegration(mlflow_uri)
        self.neptune = NeptuneIntegration(**neptune_config) if neptune_config else None
        self.current_run_id = None
        self.current_neptune_run = None
    
    async def start_experiment_tracking(self, experiment_name: str, tags: dict = None):
        """Start tracking in both MLflow and Neptune"""
        # Start MLflow experiment
        mlflow_experiment_id = await self.mlflow.create_experiment(experiment_name, tags)
        
        # Start Neptune tracking if configured
        neptune_run_id = None
        if self.neptune:
            await self.neptune.initialize_neptune_tracking(tags.get('tags', []))
            neptune_run_id = await self.neptune.create_neptune_run(experiment_name, tags.get('tags', []))
        
        return {
            'mlflow_experiment_id': mlflow_experiment_id,
            'neptune_run_id': neptune_run_id
        }
    
    async def log_experiment_data(self, params: dict, metrics: dict, artifacts: dict = None):
        """Log data to both tracking systems"""
        # Log to MLflow
        await self.mlflow.log_parameters(params)
        await self.mlflow.log_metrics(metrics)
        
        if artifacts:
            for artifact_name, artifact_path in artifacts.items():
                await self.mlflow.log_artifact(artifact_path, artifact_name)
        
        # Log to Neptune if configured
        if self.neptune and self.current_neptune_run:
            combined_data = {**params, **metrics}
            await self.neptune.log_to_neptune(self.current_neptune_run, combined_data)
```

### 8.3 Integration with Platform Services

#### 8.3.1 Experiment Service Integration
```python
class ExperimentService:
    def __init__(self, db: Session, tracking_service: DualTrackingService):
        self.db = db
        self.tracking = tracking_service
    
    async def create_experiment(self, experiment_data: ExperimentCreate, user_id: str) -> Experiment:
        """Create experiment with integrated tracking"""
        # Create database record
        db_experiment = Experiment(
            name=experiment_data.name,
            description=experiment_data.description,
            project_id=experiment_data.project_id,
            dataset_id=experiment_data.dataset_id,
            created_by=user_id,
            experiment_type=experiment_data.experiment_type,
            target_column=experiment_data.target_column,
            feature_columns=experiment_data.feature_columns
        )
        
        self.db.add(db_experiment)
        self.db.commit()
        self.db.refresh(db_experiment)
        
        # Initialize tracking
        tracking_ids = await self.tracking.start_experiment_tracking(
            experiment_name=f"{experiment_data.name}_{db_experiment.id}",
            tags={
                'project_id': str(experiment_data.project_id),
                'experiment_type': experiment_data.experiment_type,
                'tags': ['platform', 'automated']
            }
        )
        
        # Update database with tracking IDs
        db_experiment.mlflow_experiment_id = tracking_ids['mlflow_experiment_id']
        db_experiment.neptune_run_id = tracking_ids['neptune_run_id']
        self.db.commit()
        
        return db_experiment
    
    async def log_experiment_run(self, experiment_id: str, run_data: dict) -> ExperimentRun:
        """Log experiment run with tracking integration"""
        experiment = self.db.query(Experiment).filter(Experiment.id == experiment_id).first()
        
        # Start MLflow run
        mlflow_run_id = await self.tracking.mlflow.start_run(
            experiment_id=experiment.mlflow_experiment_id,
            run_name=run_data.get('run_name')
        )
        
        # Create database record
        db_run = ExperimentRun(
            experiment_id=experiment_id,
            run_name=run_data.get('run_name'),
            mlflow_run_id=mlflow_run_id,
            algorithm=run_data.get('algorithm'),
            hyperparameters=run_data.get('hyperparameters', {}),
            status='running'
        )
        
        self.db.add(db_run)
        self.db.commit()
        
        # Log initial parameters
        await self.tracking.log_experiment_data(
            params=run_data.get('hyperparameters', {}),
            metrics={},
            artifacts={}
        )
        
        return db_run
```

---

## 9. Docker Configuration and Deployment

### 9.1 Docker Architecture Overview

The platform uses a multi-container Docker architecture with separate containers for each service, orchestrated using Docker Compose for development and production deployment.

```mermaid
graph TB
    subgraph "Docker Host"
        subgraph "Frontend Tier"
            NGINX[Nginx Container]
            FRONTEND[Frontend Container]
        end
        
        subgraph "Backend Tier"
            API[API Gateway Container]
            AUTH[Auth Service Container]
            DATA[Data Service Container]
            EXP[Experiment Service Container]
            AGENTS[AI Agents Container]
        end
        
        subgraph "ML Platform Tier"
            MLFLOW[MLflow Container]
            NEPTUNE[Neptune Integration]
        end
        
        subgraph "Data Tier"
            POSTGRES[PostgreSQL Container]
            REDIS[Redis Container]
            MINIO[MinIO Container]
        end
    end
    
    NGINX --> FRONTEND
    NGINX --> API
    API --> AUTH
    API --> DATA
    API --> EXP
    API --> AGENTS
    EXP --> MLFLOW
    AUTH --> POSTGRES
    DATA --> POSTGRES
    EXP --> POSTGRES
    API --> REDIS
    DATA --> MINIO
    MLFLOW --> POSTGRES
    MLFLOW --> MINIO
```

### 9.2 Dockerfile Specifications

#### 9.2.1 Frontend Dockerfile
```dockerfile
# Frontend Dockerfile
FROM node:20-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./
RUN \
  if [ -f yarn.lock ]; then yarn --frozen-lockfile; \
  elif [ -f package-lock.json ]; then npm ci; \
  elif [ -f pnpm-lock.yaml ]; then yarn global add pnpm && pnpm i --frozen-lockfile; \
  else echo "Lockfile not found." && exit 1; \
  fi

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build the application
RUN yarn build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

#### 9.2.2 Backend API Dockerfile
```dockerfile
# Backend API Dockerfile
FROM python:3.11-slim AS base

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set work directory
WORKDIR /app

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Change ownership to app user
RUN chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run the application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### 9.2.3 AI Agents Dockerfile
```dockerfile
# AI Agents Dockerfile
FROM python:3.11-slim AS base

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1

# Install system dependencies for ML libraries
RUN apt-get update && apt-get install -y \
    build-essential \
    libgomp1 \
    libatlas-base-dev \
    liblapack-dev \
    gfortran \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN groupadd -r agentuser && useradd -r -g agentuser agentuser

# Set work directory
WORKDIR /app

# Install Python dependencies
COPY requirements-agents.txt .
RUN pip install --no-cache-dir -r requirements-agents.txt

# Copy agent code
COPY agents/ ./agents/
COPY shared/ ./shared/

# Change ownership
RUN chown -R agentuser:agentuser /app
USER agentuser

# Expose port for agent coordinator
EXPOSE 8001

# Run the agent coordinator
CMD ["python", "-m", "agents.coordinator"]
```

### 9.3 Docker Compose Configuration

#### 9.3.1 Development Docker Compose
```yaml
# docker-compose.dev.yml
version: '3.8'

services:
  # Frontend Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    depends_on:
      - api
    networks:
      - ml-platform-network

  # API Gateway
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
    environment:
      - DATABASE_URL=********************************************/ml_platform
      - REDIS_URL=redis://redis:6379/0
      - MLFLOW_TRACKING_URI=http://mlflow:5000
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
    depends_on:
      - postgres
      - redis
      - mlflow
    networks:
      - ml-platform-network

  # AI Agents Service
  agents:
    build:
      context: ./agents
      dockerfile: Dockerfile.dev
    ports:
      - "8001:8001"
    volumes:
      - ./agents:/app
    environment:
      - PLATFORM_API_URL=http://api:8000
      - REDIS_URL=redis://redis:6379/1
    depends_on:
      - api
      - redis
    networks:
      - ml-platform-network

  # MLflow Server
  mlflow:
    image: ghcr.io/mlflow/mlflow:v2.19.0
    ports:
      - "5000:5000"
    environment:
      - MLFLOW_BACKEND_STORE_URI=postgresql+psycopg2://mlflow:mlflow123@postgres:5432/mlflow
      - MLFLOW_DEFAULT_ARTIFACT_ROOT=s3://mlflow-artifacts/
      - AWS_ACCESS_KEY_ID=minioadmin
      - AWS_SECRET_ACCESS_KEY=minioadmin
      - MLFLOW_S3_ENDPOINT_URL=http://minio:9000
    command: >
      mlflow server
      --backend-store-uri postgresql+psycopg2://mlflow:mlflow123@postgres:5432/mlflow
      --default-artifact-root s3://mlflow-artifacts/
      --host 0.0.0.0
      --port 5000
    depends_on:
      - postgres
      - minio
    networks:
      - ml-platform-network

  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=ml_platform
      - POSTGRES_MULTIPLE_DATABASES=mlflow
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-multiple-databases.sh:/docker-entrypoint-initdb.d/init-multiple-databases.sh
    networks:
      - ml-platform-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ml-platform-network

  # MinIO Object Storage
  minio:
    image: minio/minio:latest
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    networks:
      - ml-platform-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.dev.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - api
    networks:
      - ml-platform-network

volumes:
  postgres_data:
  redis_data:
  minio_data:

networks:
  ml-platform-network:
    driver: bridge
```

#### 9.3.2 Production Docker Compose
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  # Frontend Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=https://api.mlplatform.com
    networks:
      - ml-platform-network

  # API Gateway
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile
    restart: unless-stopped
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - MLFLOW_TRACKING_URI=${MLFLOW_TRACKING_URI}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - MINIO_ENDPOINT=${MINIO_ENDPOINT}
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
    depends_on:
      - postgres
      - redis
      - mlflow
    networks:
      - ml-platform-network
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # AI Agents Service
  agents:
    build:
      context: ./agents
      dockerfile: Dockerfile
    restart: unless-stopped
    environment:
      - PLATFORM_API_URL=http://api:8000
      - REDIS_URL=${REDIS_URL}
    depends_on:
      - api
      - redis
    networks:
      - ml-platform-network
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  # MLflow Server
  mlflow:
    image: ghcr.io/mlflow/mlflow:v2.19.0
    restart: unless-stopped
    environment:
      - MLFLOW_BACKEND_STORE_URI=${MLFLOW_BACKEND_STORE_URI}
      - MLFLOW_DEFAULT_ARTIFACT_ROOT=${MLFLOW_DEFAULT_ARTIFACT_ROOT}
      - AWS_ACCESS_KEY_ID=${MINIO_ACCESS_KEY}
      - AWS_SECRET_ACCESS_KEY=${MINIO_SECRET_KEY}
      - MLFLOW_S3_ENDPOINT_URL=${MLFLOW_S3_ENDPOINT_URL}
    command: >
      mlflow server
      --backend-store-uri ${MLFLOW_BACKEND_STORE_URI}
      --default-artifact-root ${MLFLOW_DEFAULT_ARTIFACT_ROOT}
      --host 0.0.0.0
      --port 5000
    depends_on:
      - postgres
      - minio
    networks:
      - ml-platform-network

  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    restart: unless-stopped
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - ml-platform-network
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Redis Cache
  redis:
    image: redis:7-alpine
    restart: unless-stopped
    volumes:
      - redis_data:/data
    networks:
      - ml-platform-network

  # MinIO Object Storage
  minio:
    image: minio/minio:latest
    restart: unless-stopped
    environment:
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    networks:
      - ml-platform-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - /etc/letsencrypt:/etc/letsencrypt:ro
    depends_on:
      - frontend
      - api
    networks:
      - ml-platform-network

volumes:
  postgres_data:
  redis_data:
  minio_data:

networks:
  ml-platform-network:
    driver: bridge
```

### 9.4 Nginx Configuration

#### 9.4.1 Production Nginx Configuration
```nginx
# nginx/nginx.prod.conf
events {
    worker_connections 1024;
}

http {
    upstream frontend {
        server frontend:3000;
    }
    
    upstream api {
        server api:8000;
    }
    
    upstream mlflow {
        server mlflow:5000;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;

    # SSL Configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # HTTP to HTTPS redirect
    server {
        listen 80;
        server_name mlplatform.com www.mlplatform.com;
        return 301 https://$server_name$request_uri;
    }

    # Main HTTPS server
    server {
        listen 443 ssl http2;
        server_name mlplatform.com www.mlplatform.com;

        ssl_certificate /etc/letsencrypt/live/mlplatform.com/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/mlplatform.com/privkey.pem;

        # Frontend
        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # API endpoints
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Authentication endpoints with stricter rate limiting
        location /api/v1/auth/ {
            limit_req zone=auth burst=10 nodelay;
            proxy_pass http://api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # MLflow UI
        location /mlflow/ {
            proxy_pass http://mlflow/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # File upload size limit
        client_max_body_size 100M;
    }
}
```

### 9.5 Deployment Scripts

#### 9.5.1 Production Deployment Script
```bash
#!/bin/bash
# deploy.sh - Production deployment script

set -e

echo "Starting ML Platform deployment..."

# Load environment variables
if [ -f .env.prod ]; then
    export $(cat .env.prod | xargs)
else
    echo "Error: .env.prod file not found"
    exit 1
fi

# Create necessary directories
mkdir -p logs
mkdir -p data/postgres
mkdir -p data/redis
mkdir -p data/minio

# Pull latest images
echo "Pulling latest Docker images..."
docker-compose -f docker-compose.prod.yml pull

# Build custom images
echo "Building custom images..."
docker-compose -f docker-compose.prod.yml build --no-cache

# Stop existing containers
echo "Stopping existing containers..."
docker-compose -f docker-compose.prod.yml down

# Start services
echo "Starting services..."
docker-compose -f docker-compose.prod.yml up -d

# Wait for services to be ready
echo "Waiting for services to be ready..."
sleep 30

# Run database migrations
echo "Running database migrations..."
docker-compose -f docker-compose.prod.yml exec api alembic upgrade head

# Create MLflow bucket in MinIO
echo "Setting up MLflow artifacts bucket..."
docker-compose -f docker-compose.prod.yml exec minio mc mb /data/mlflow-artifacts || true

# Health check
echo "Performing health checks..."
for service in api frontend mlflow postgres redis minio; do
    if docker-compose -f docker-compose.prod.yml ps $service | grep -q "Up"; then
        echo "✓ $service is running"
    else
        echo "✗ $service is not running"
        exit 1
    fi
done

echo "Deployment completed successfully!"
echo "Platform is available at: https://mlplatform.com"
echo "MLflow UI is available at: https://mlplatform.com/mlflow"
```

#### 9.5.2 Environment Configuration
```bash
# .env.prod - Production environment variables
# Database Configuration
DATABASE_URL=******************************************************/ml_platform
POSTGRES_USER=ml_platform
POSTGRES_PASSWORD=secure_password
POSTGRES_DB=ml_platform

# Redis Configuration
REDIS_URL=redis://redis:6379/0

# MLflow Configuration
MLFLOW_TRACKING_URI=http://mlflow:5000
MLFLOW_BACKEND_STORE_URI=postgresql+psycopg2://mlflow:mlflow_password@postgres:5432/mlflow
MLFLOW_DEFAULT_ARTIFACT_ROOT=s3://mlflow-artifacts/
MLFLOW_S3_ENDPOINT_URL=http://minio:9000

# MinIO Configuration
MINIO_ACCESS_KEY=ml_platform_access
MINIO_SECRET_KEY=ml_platform_secret_key_very_secure
MINIO_ENDPOINT=minio:9000

# Security Configuration
JWT_SECRET_KEY=your_very_secure_jwt_secret_key_here
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# Neptune.AI Configuration (Optional)
NEPTUNE_API_TOKEN=your_neptune_api_token
NEPTUNE_PROJECT=your_workspace/your_project

# Application Configuration
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO
```

---

## 10. Security and Authentication

### 10.1 Security Architecture Overview

The platform implements a comprehensive security model with multiple layers of protection, following industry best practices for web application security.

```mermaid
graph TB
    subgraph "Security Layers"
        subgraph "Network Security"
            HTTPS[HTTPS/TLS 1.3]
            FIREWALL[Firewall Rules]
            RATE_LIMIT[Rate Limiting]
        end
        
        subgraph "Application Security"
            AUTH[JWT Authentication]
            AUTHZ[Role-Based Authorization]
            VALIDATION[Input Validation]
            CSRF[CSRF Protection]
        end
        
        subgraph "Data Security"
            ENCRYPTION[Data Encryption]
            BACKUP[Secure Backups]
            AUDIT[Audit Logging]
        end
        
        subgraph "Infrastructure Security"
            CONTAINER[Container Security]
            SECRETS[Secrets Management]
            MONITORING[Security Monitoring]
        end
    end
```

### 10.2 Authentication System

#### 10.2.1 JWT-Based Authentication
The platform uses JSON Web Tokens (JWT) for stateless authentication, implementing best practices from the research gathered:

```python
# Authentication Service Implementation
import jwt
import bcrypt
from datetime import datetime, timedelta
from typing import Optional
import secrets

class AuthenticationService:
    def __init__(self, secret_key: str, algorithm: str = "HS256"):
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.access_token_expire_minutes = 30
        self.refresh_token_expire_days = 7
    
    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt"""
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def verify_password(self, password: str, hashed_password: str) -> bool:
        """Verify password against hash"""
        return bcrypt.checkpw(password.encode('utf-8'), hashed_password.encode('utf-8'))
    
    def create_access_token(self, user_id: str, additional_claims: dict = None) -> str:
        """Create JWT access token"""
        now = datetime.utcnow()
        expire = now + timedelta(minutes=self.access_token_expire_minutes)
        
        payload = {
            "sub": user_id,
            "iat": now,
            "exp": expire,
            "type": "access",
            "jti": secrets.token_urlsafe(32)  # Unique token ID
        }
        
        if additional_claims:
            payload.update(additional_claims)
        
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def create_refresh_token(self, user_id: str) -> str:
        """Create JWT refresh token"""
        now = datetime.utcnow()
        expire = now + timedelta(days=self.refresh_token_expire_days)
        
        payload = {
            "sub": user_id,
            "iat": now,
            "exp": expire,
            "type": "refresh",
            "jti": secrets.token_urlsafe(32)
        }
        
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def verify_token(self, token: str, token_type: str = "access") -> Optional[dict]:
        """Verify and decode JWT token"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            if payload.get("type") != token_type:
                return None
            
            # Check if token is blacklisted
            if self.is_token_blacklisted(payload.get("jti")):
                return None
            
            return payload
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None
    
    def blacklist_token(self, token: str):
        """Add token to blacklist"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            jti = payload.get("jti")
            exp = payload.get("exp")
            
            if jti and exp:
                # Store in Redis with expiration
                redis_client.setex(f"blacklist:{jti}", exp - int(datetime.utcnow().timestamp()), "1")
        except jwt.InvalidTokenError:
            pass
    
    def is_token_blacklisted(self, jti: str) -> bool:
        """Check if token is blacklisted"""
        return redis_client.exists(f"blacklist:{jti}")
```

#### 10.2.2 Session Management
```python
class SessionManager:
    def __init__(self, redis_client):
        self.redis = redis_client
        self.session_timeout = 3600  # 1 hour
    
    async def create_session(self, user_id: str, token_jti: str) -> str:
        """Create user session"""
        session_id = secrets.token_urlsafe(32)
        session_data = {
            "user_id": user_id,
            "token_jti": token_jti,
            "created_at": datetime.utcnow().isoformat(),
            "last_activity": datetime.utcnow().isoformat()
        }
        
        await self.redis.setex(
            f"session:{session_id}",
            self.session_timeout,
            json.dumps(session_data)
        )
        
        return session_id
    
    async def validate_session(self, session_id: str) -> Optional[dict]:
        """Validate and refresh session"""
        session_data = await self.redis.get(f"session:{session_id}")
        
        if not session_data:
            return None
        
        session = json.loads(session_data)
        
        # Update last activity
        session["last_activity"] = datetime.utcnow().isoformat()
        await self.redis.setex(
            f"session:{session_id}",
            self.session_timeout,
            json.dumps(session)
        )
        
        return session
    
    async def invalidate_session(self, session_id: str):
        """Invalidate user session"""
        await self.redis.delete(f"session:{session_id}")
```

### 10.3 Authorization and Access Control

#### 10.3.1 Role-Based Access Control (RBAC)
```python
from enum import Enum
from typing import List, Set

class Role(Enum):
    ADMIN = "admin"
    PROJECT_OWNER = "project_owner"
    PROJECT_MEMBER = "project_member"
    VIEWER = "viewer"

class Permission(Enum):
    # Project permissions
    CREATE_PROJECT = "create_project"
    DELETE_PROJECT = "delete_project"
    UPDATE_PROJECT = "update_project"
    VIEW_PROJECT = "view_project"
    
    # Dataset permissions
    UPLOAD_DATASET = "upload_dataset"
    DELETE_DATASET = "delete_dataset"
    VIEW_DATASET = "view_dataset"
    
    # Experiment permissions
    CREATE_EXPERIMENT = "create_experiment"
    DELETE_EXPERIMENT = "delete_experiment"
    VIEW_EXPERIMENT = "view_experiment"
    
    # Agent permissions
    USE_AGENTS = "use_agents"
    
    # Admin permissions
    MANAGE_USERS = "manage_users"
    SYSTEM_CONFIG = "system_config"

class RolePermissions:
    ROLE_PERMISSIONS = {
        Role.ADMIN: {
            Permission.CREATE_PROJECT,
            Permission.DELETE_PROJECT,
            Permission.UPDATE_PROJECT,
            Permission.VIEW_PROJECT,
            Permission.UPLOAD_DATASET,
            Permission.DELETE_DATASET,
            Permission.VIEW_DATASET,
            Permission.CREATE_EXPERIMENT,
            Permission.DELETE_EXPERIMENT,
            Permission.VIEW_EXPERIMENT,
            Permission.USE_AGENTS,
            Permission.MANAGE_USERS,
            Permission.SYSTEM_CONFIG,
        },
        Role.PROJECT_OWNER: {
            Permission.CREATE_PROJECT,
            Permission.DELETE_PROJECT,
            Permission.UPDATE_PROJECT,
            Permission.VIEW_PROJECT,
            Permission.UPLOAD_DATASET,
            Permission.DELETE_DATASET,
            Permission.VIEW_DATASET,
            Permission.CREATE_EXPERIMENT,
            Permission.DELETE_EXPERIMENT,
            Permission.VIEW_EXPERIMENT,
            Permission.USE_AGENTS,
        },
        Role.PROJECT_MEMBER: {
            Permission.VIEW_PROJECT,
            Permission.UPLOAD_DATASET,
            Permission.VIEW_DATASET,
            Permission.CREATE_EXPERIMENT,
            Permission.VIEW_EXPERIMENT,
            Permission.USE_AGENTS,
        },
        Role.VIEWER: {
            Permission.VIEW_PROJECT,
            Permission.VIEW_DATASET,
            Permission.VIEW_EXPERIMENT,
        }
    }
    
    @classmethod
    def get_permissions(cls, role: Role) -> Set[Permission]:
        return cls.ROLE_PERMISSIONS.get(role, set())
    
    @classmethod
    def has_permission(cls, role: Role, permission: Permission) -> bool:
        return permission in cls.get_permissions(role)

class AuthorizationService:
    def __init__(self, db: Session):
        self.db = db
    
    def check_permission(self, user_id: str, permission: Permission, resource_id: str = None) -> bool:
        """Check if user has permission for a specific action"""
        user = self.db.query(User).filter(User.id == user_id).first()
        
        if not user or not user.is_active:
            return False
        
        # Admin users have all permissions
        if user.is_superuser:
            return True
        
        # Check global permissions
        if RolePermissions.has_permission(Role.ADMIN, permission) and user.is_superuser:
            return True
        
        # Check project-specific permissions
        if resource_id:
            project_member = self.db.query(ProjectMember).filter(
                ProjectMember.user_id == user_id,
                ProjectMember.project_id == resource_id
            ).first()
            
            if project_member:
                role = Role(project_member.role)
                return RolePermissions.has_permission(role, permission)
        
        return False
    
    def require_permission(self, permission: Permission, resource_id: str = None):
        """Decorator to require specific permission"""
        def decorator(func):
            async def wrapper(*args, **kwargs):
                current_user = kwargs.get('current_user')
                if not current_user:
                    raise HTTPException(status_code=401, detail="Authentication required")
                
                if not self.check_permission(current_user.id, permission, resource_id):
                    raise HTTPException(status_code=403, detail="Insufficient permissions")
                
                return await func(*args, **kwargs)
            return wrapper
        return decorator
```

### 10.4 Input Validation and Sanitization

#### 10.4.1 Pydantic Validation Models
```python
from pydantic import BaseModel, validator, Field
from typing import Optional, List
import re

class UserCreate(BaseModel):
    email: str = Field(..., regex=r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    username: str = Field(..., min_length=3, max_length=50, regex=r'^[a-zA-Z0-9_-]+$')
    password: str = Field(..., min_length=8, max_length=128)
    first_name: Optional[str] = Field(None, max_length=100)
    last_name: Optional[str] = Field(None, max_length=100)
    
    @validator('password')
    def validate_password_strength(cls, v):
        """Validate password strength"""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        
        if not re.search(r'\d', v):
            raise ValueError('Password must contain at least one digit')
        
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError('Password must contain at least one special character')
        
        return v

class ProjectCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    
    @validator('name')
    def validate_project_name(cls, v):
        """Validate project name"""
        if not re.match(r'^[a-zA-Z0-9\s_-]+$', v):
            raise ValueError('Project name can only contain letters, numbers, spaces, hyphens, and underscores')
        return v.strip()

class DatasetUpload(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    project_id: str = Field(..., regex=r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$')
    
    @validator('name')
    def validate_dataset_name(cls, v):
        """Validate dataset name"""
        if not re.match(r'^[a-zA-Z0-9\s._-]+$', v):
            raise ValueError('Dataset name contains invalid characters')
        return v.strip()
```

#### 10.4.2 File Upload Security
```python
import magic
from pathlib import Path

class FileUploadSecurity:
    ALLOWED_EXTENSIONS = {'.csv', '.xlsx', '.xls', '.json', '.parquet'}
    MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
    ALLOWED_MIME_TYPES = {
        'text/csv',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/json',
        'application/octet-stream'  # For parquet files
    }
    
    @staticmethod
    def validate_file(file: UploadFile) -> bool:
        """Validate uploaded file"""
        # Check file size
        if file.size > FileUploadSecurity.MAX_FILE_SIZE:
            raise ValueError(f"File size exceeds maximum allowed size of {FileUploadSecurity.MAX_FILE_SIZE} bytes")
        
        # Check file extension
        file_extension = Path(file.filename).suffix.lower()
        if file_extension not in FileUploadSecurity.ALLOWED_EXTENSIONS:
            raise ValueError(f"File extension {file_extension} is not allowed")
        
        # Check MIME type
        file_content = file.file.read(1024)  # Read first 1KB
        file.file.seek(0)  # Reset file pointer
        
        mime_type = magic.from_buffer(file_content, mime=True)
        if mime_type not in FileUploadSecurity.ALLOWED_MIME_TYPES:
            raise ValueError(f"File MIME type {mime_type} is not allowed")
        
        return True
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """Sanitize uploaded filename"""
        # Remove path components
        filename = Path(filename).name
        
        # Remove or replace dangerous characters
        filename = re.sub(r'[^\w\s.-]', '', filename)
        filename = re.sub(r'[-\s]+', '-', filename)
        
        # Limit length
        if len(filename) > 255:
            name, ext = Path(filename).stem, Path(filename).suffix
            filename = name[:255-len(ext)] + ext
        
        return filename
```

### 10.5 HTTPS and TLS Configuration

#### 10.5.1 SSL/TLS Configuration
```nginx
# SSL/TLS Configuration in Nginx
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
ssl_prefer_server_ciphers off;
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;
ssl_session_tickets off;

# OCSP Stapling
ssl_stapling on;
ssl_stapling_verify on;
resolver ******* ******* valid=300s;
resolver_timeout 5s;

# Security Headers
add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
add_header X-Frame-Options DENY always;
add_header X-Content-Type-Options nosniff always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self' wss:; frame-ancestors 'none';" always;
```

### 10.6 Rate Limiting and DDoS Protection

#### 10.6.1 Application-Level Rate Limiting
```python
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

# Initialize rate limiter
limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

# Apply rate limiting to endpoints
@app.post("/api/v1/auth/login")
@limiter.limit("5/minute")
async def login(request: Request, credentials: UserLogin):
    """Login endpoint with rate limiting"""
    pass

@app.post("/api/v1/datasets/upload")
@limiter.limit("10/hour")
async def upload_dataset(request: Request, file: UploadFile):
    """Dataset upload with rate limiting"""
    pass

@app.get("/api/v1/experiments")
@limiter.limit("100/minute")
async def list_experiments(request: Request):
    """List experiments with rate limiting"""
    pass
```

### 10.7 Audit Logging and Monitoring

#### 10.7.1 Security Audit Logging
```python
import logging
from datetime import datetime
from typing import Optional

class SecurityAuditLogger:
    def __init__(self):
        self.logger = logging.getLogger("security_audit")
        handler = logging.FileHandler("logs/security_audit.log")
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
    
    def log_authentication_attempt(self, username: str, ip_address: str, success: bool, reason: str = None):
        """Log authentication attempts"""
        status = "SUCCESS" if success else "FAILURE"
        message = f"AUTH_{status} - User: {username}, IP: {ip_address}"
        if reason:
            message += f", Reason: {reason}"
        
        if success:
            self.logger.info(message)
        else:
            self.logger.warning(message)
    
    def log_authorization_failure(self, user_id: str, resource: str, action: str, ip_address: str):
        """Log authorization failures"""
        message = f"AUTHZ_FAILURE - User: {user_id}, Resource: {resource}, Action: {action}, IP: {ip_address}"
        self.logger.warning(message)
    
    def log_data_access(self, user_id: str, resource_type: str, resource_id: str, action: str):
        """Log data access events"""
        message = f"DATA_ACCESS - User: {user_id}, Type: {resource_type}, ID: {resource_id}, Action: {action}"
        self.logger.info(message)
    
    def log_security_event(self, event_type: str, details: dict, severity: str = "INFO"):
        """Log general security events"""
        message = f"SECURITY_EVENT - Type: {event_type}, Details: {details}"
        
        if severity == "CRITICAL":
            self.logger.critical(message)
        elif severity == "ERROR":
            self.logger.error(message)
        elif severity == "WARNING":
            self.logger.warning(message)
        else:
            self.logger.info(message)

# Usage in endpoints
audit_logger = SecurityAuditLogger()

@app.post("/api/v1/auth/login")
async def login(credentials: UserLogin, request: Request):
    ip_address = request.client.host
    
    try:
        # Authentication logic
        user = authenticate_user(credentials.username, credentials.password)
        if user:
            audit_logger.log_authentication_attempt(
                credentials.username, ip_address, True
            )
            return {"access_token": create_access_token(user.id)}
        else:
            audit_logger.log_authentication_attempt(
                credentials.username, ip_address, False, "Invalid credentials"
            )
            raise HTTPException(status_code=401, detail="Invalid credentials")
    except Exception as e:
        audit_logger.log_authentication_attempt(
            credentials.username, ip_address, False, str(e)
        )
        raise
```

---

## 11. Development Workflow and CI/CD

### 11.1 Development Workflow Overview

The platform follows a GitFlow-based development workflow with automated CI/CD pipelines using GitHub Actions, implementing best practices gathered from the research.

```mermaid
graph LR
    subgraph "Development Flow"
        DEV[Developer]
        FEATURE[Feature Branch]
        PR[Pull Request]
        REVIEW[Code Review]
        MAIN[Main Branch]
        STAGING[Staging Environment]
        PROD[Production Environment]
    end
    
    DEV --> FEATURE
    FEATURE --> PR
    PR --> REVIEW
    REVIEW --> MAIN
    MAIN --> STAGING
    STAGING --> PROD
    
    subgraph "CI/CD Pipeline"
        BUILD[Build & Test]
        SECURITY[Security Scan]
        DOCKER[Docker Build]
        DEPLOY[Deploy]
    end
    
    PR --> BUILD
    BUILD --> SECURITY
    SECURITY --> DOCKER
    DOCKER --> DEPLOY
```

### 11.2 GitHub Actions CI/CD Pipeline

#### 11.2.1 Main CI/CD Workflow
```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Code Quality and Testing
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.11]
        node-version: [20]
    
    services:
      postgres:
        image: postgres:16
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}

    - name: Cache Python dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements*.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Cache Node dependencies
      uses: actions/cache@v3
      with:
        path: ~/.npm
        key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
        restore-keys: |
          ${{ runner.os }}-node-

    - name: Install Python dependencies
      run: |
        cd backend
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt

    - name: Install Node dependencies
      run: |
        cd frontend
        npm ci

    - name: Run Python linting
      run: |
        cd backend
        flake8 app/ --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 app/ --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

    - name: Run Python type checking
      run: |
        cd backend
        mypy app/

    - name: Run Python tests
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379/0
        JWT_SECRET_KEY: test_secret_key
      run: |
        cd backend
        pytest tests/ -v --cov=app --cov-report=xml

    - name: Run Frontend linting
      run: |
        cd frontend
        npm run lint

    - name: Run Frontend type checking
      run: |
        cd frontend
        npm run type-check

    - name: Run Frontend tests
      run: |
        cd frontend
        npm run test

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage.xml
        flags: backend
        name: backend-coverage

  # Security Scanning
  security:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

    - name: Run Bandit security linter
      run: |
        pip install bandit
        bandit -r backend/app/ -f json -o bandit-report.json

    - name: Run npm audit
      run: |
        cd frontend
        npm audit --audit-level moderate

  # Build and Push Docker Images
  build:
    runs-on: ubuntu-latest
    needs: [test, security]
    if: github.event_name == 'push'
    
    permissions:
      contents: read
      packages: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata for Frontend
      id: meta-frontend
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-frontend
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Frontend image
      uses: docker/build-push-action@v5
      with:
        context: ./frontend
        push: true
        tags: ${{ steps.meta-frontend.outputs.tags }}
        labels: ${{ steps.meta-frontend.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Extract metadata for Backend
      id: meta-backend
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-backend
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Backend image
      uses: docker/build-push-action@v5
      with:
        context: ./backend
        push: true
        tags: ${{ steps.meta-backend.outputs.tags }}
        labels: ${{ steps.meta-backend.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Extract metadata for Agents
      id: meta-agents
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-agents
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Agents image
      uses: docker/build-push-action@v5
      with:
        context: ./agents
        push: true
        tags: ${{ steps.meta-agents.outputs.tags }}
        labels: ${{ steps.meta-agents.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # Deploy to Staging
  deploy-staging:
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to staging
      uses: appleboy/ssh-action@v1.0.0
      with:
        host: ${{ secrets.STAGING_HOST }}
        username: ${{ secrets.STAGING_USER }}
        key: ${{ secrets.STAGING_SSH_KEY }}
        script: |
          cd /opt/ml-platform
          git pull origin develop
          docker-compose -f docker-compose.staging.yml pull
          docker-compose -f docker-compose.staging.yml up -d
          docker system prune -f

    - name: Run health checks
      run: |
        sleep 30
        curl -f ${{ secrets.STAGING_URL }}/health || exit 1

  # Deploy to Production
  deploy-production:
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to production
      uses: appleboy/ssh-action@v1.0.0
      with:
        host: ${{ secrets.PRODUCTION_HOST }}
        username: ${{ secrets.PRODUCTION_USER }}
        key: ${{ secrets.PRODUCTION_SSH_KEY }}
        script: |
          cd /opt/ml-platform
          git pull origin main
          docker-compose -f docker-compose.prod.yml pull
          docker-compose -f docker-compose.prod.yml up -d
          docker system prune -f

    - name: Run health checks
      run: |
        sleep 60
        curl -f ${{ secrets.PRODUCTION_URL }}/health || exit 1

    - name: Notify deployment
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
      if: always()
```

#### 11.2.2 Database Migration Workflow
```yaml
# .github/workflows/database-migration.yml
name: Database Migration

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'backend/migrations/**'

jobs:
  migrate:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11

    - name: Install dependencies
      run: |
        cd backend
        pip install -r requirements.txt

    - name: Run migrations on staging
      if: github.ref == 'refs/heads/develop'
      env:
        DATABASE_URL: ${{ secrets.STAGING_DATABASE_URL }}
      run: |
        cd backend
        alembic upgrade head

    - name: Run migrations on production
      if: github.ref == 'refs/heads/main'
      env:
        DATABASE_URL: ${{ secrets.PRODUCTION_DATABASE_URL }}
      run: |
        cd backend
        alembic upgrade head
```

### 11.3 Development Environment Setup

#### 11.3.1 Local Development Setup Script
```bash
#!/bin/bash
# scripts/setup-dev.sh

set -e

echo "Setting up ML Platform development environment..."

# Check prerequisites
command -v docker >/dev/null 2>&1 || { echo "Docker is required but not installed. Aborting." >&2; exit 1; }
command -v docker-compose >/dev/null 2>&1 || { echo "Docker Compose is required but not installed. Aborting." >&2; exit 1; }
command -v node >/dev/null 2>&1 || { echo "Node.js is required but not installed. Aborting." >&2; exit 1; }
command -v python3 >/dev/null 2>&1 || { echo "Python 3 is required but not installed. Aborting." >&2; exit 1; }

# Create environment files
if [ ! -f .env.dev ]; then
    echo "Creating development environment file..."
    cp .env.example .env.dev
    echo "Please edit .env.dev with your configuration"
fi

# Set up Python virtual environment
echo "Setting up Python virtual environment..."
cd backend
python3 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
pip install -r requirements-dev.txt

# Set up pre-commit hooks
echo "Setting up pre-commit hooks..."
pre-commit install

# Set up Node.js dependencies
echo "Setting up Node.js dependencies..."
cd ../frontend
npm install

# Start development services
echo "Starting development services..."
cd ..
docker-compose -f docker-compose.dev.yml up -d postgres redis minio

# Wait for services to be ready
echo "Waiting for services to be ready..."
sleep 10

# Run database migrations
echo "Running database migrations..."
cd backend
source venv/bin/activate
alembic upgrade head

# Create initial data
echo "Creating initial development data..."
python scripts/create_dev_data.py

echo "Development environment setup complete!"
echo "To start the development servers:"
echo "  Backend: cd backend && source venv/bin/activate && uvicorn app.main:app --reload"
echo "  Frontend: cd frontend && npm run dev"
echo "  Full stack: docker-compose -f docker-compose.dev.yml up"
```

#### 11.3.2 Pre-commit Configuration
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict

  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        files: ^backend/
        language_version: python3

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        files: ^backend/
        args: ["--profile", "black"]

  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        files: ^backend/
        args: [--max-line-length=88, --extend-ignore=E203]

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.3.0
    hooks:
      - id: mypy
        files: ^backend/
        additional_dependencies: [types-all]

  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.42.0
    hooks:
      - id: eslint
        files: ^frontend/
        types: [file]
        types_or: [javascript, jsx, ts, tsx]
        additional_dependencies:
          - eslint@8.42.0
          - "@typescript-eslint/eslint-plugin@5.59.8"
          - "@typescript-eslint/parser@5.59.8"

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.0.0
    hooks:
      - id: prettier
        files: ^frontend/
        types_or: [javascript, jsx, ts, tsx, json, yaml, markdown]
```

### 11.4 Testing Strategy

#### 11.4.1 Backend Testing Configuration
```python
# backend/tests/conftest.py
import pytest
import asyncio
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.core.database import get_db, Base
from app.core.config import settings

# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
def db():
    """Create a test database session."""
    Base.metadata.create_all(bind=engine)
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()
        Base.metadata.drop_all(bind=engine)

@pytest.fixture
def client(db):
    """Create a test client."""
    def override_get_db():
        try:
            yield db
        finally:
            db.close()
    
    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as test_client:
        yield test_client
    app.dependency_overrides.clear()

@pytest.fixture
def test_user(db):
    """Create a test user."""
    from app.models.user import User
    from app.core.security import get_password_hash
    
    user = User(
        email="<EMAIL>",
        username="testuser",
        password_hash=get_password_hash("testpassword"),
        first_name="Test",
        last_name="User",
        is_active=True
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    return user

@pytest.fixture
def auth_headers(client, test_user):
    """Get authentication headers for test user."""
    response = client.post(
        "/api/v1/auth/login",
        json={"username": "testuser", "password": "testpassword"}
    )
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}
```

#### 11.4.2 Frontend Testing Configuration
```javascript
// frontend/jest.config.js
const nextJest = require('next/jest')

const createJestConfig = nextJest({
  dir: './',
})

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapping: {
    '^@/components/(.*)$': '<rootDir>/components/$1',
    '^@/pages/(.*)$': '<rootDir>/pages/$1',
    '^@/lib/(.*)$': '<rootDir>/lib/$1',
  },
  testEnvironment: 'jest-environment-jsdom',
  collectCoverageFrom: [
    'components/**/*.{js,jsx,ts,tsx}',
    'pages/**/*.{js,jsx,ts,tsx}',
    'lib/**/*.{js,jsx,ts,tsx}',
    '!**/*.d.ts',
    '!**/node_modules/**',
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
  },
}

module.exports = createJestConfig(customJestConfig)
```

### 11.5 Monitoring and Alerting

#### 11.5.1 Application Monitoring
```yaml
# monitoring/docker-compose.monitoring.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
      - ./grafana/dashboards:/var/lib/grafana/dashboards

  alertmanager:
    image: prom/alertmanager:latest
    ports:
      - "9093:9093"
    volumes:
      - ./alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - alertmanager_data:/alertmanager

volumes:
  prometheus_data:
  grafana_data:
  alertmanager_data:
```

#### 11.5.2 Application Metrics
```python
# backend/app/core/metrics.py
from prometheus_client import Counter, Histogram, Gauge, generate_latest
import time
from functools import wraps

# Define metrics
REQUEST_COUNT = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status']
)

REQUEST_DURATION = Histogram(
    'http_request_duration_seconds',
    'HTTP request duration',
    ['method', 'endpoint']
)

ACTIVE_EXPERIMENTS = Gauge(
    'active_experiments_total',
    'Number of active experiments'
)

AGENT_TASKS = Counter(
    'agent_tasks_total',
    'Total agent tasks',
    ['task_type', 'status']
)

def track_requests(func):
    """Decorator to track request metrics"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = await func(*args, **kwargs)
            REQUEST_COUNT.labels(
                method='POST',  # This would be dynamic
                endpoint=func.__name__,
                status='success'
            ).inc()
            return result
        except Exception as e:
            REQUEST_COUNT.labels(
                method='POST',
                endpoint=func.__name__,
                status='error'
            ).inc()
            raise
        finally:
            REQUEST_DURATION.labels(
                method='POST',
                endpoint=func.__name__
            ).observe(time.time() - start_time)
    
    return wrapper

# Metrics endpoint
@app.get("/metrics")
async def get_metrics():
    return Response(generate_latest(), media_type="text/plain")
```

---

## 12. Performance and Scalability Considerations

### 12.1 Performance Architecture Overview

The platform is designed to handle the specified scale (5 concurrent users, 8GB datasets, hundreds of experiments) with room for growth through horizontal scaling and performance optimizations.

```mermaid
graph TB
    subgraph "Performance Layers"
        subgraph "Frontend Performance"
            CDN[CDN/Edge Caching]
            LAZY[Lazy Loading]
            BUNDLE[Bundle Optimization]
        end
        
        subgraph "API Performance"
            CACHE[Redis Caching]
            POOL[Connection Pooling]
            ASYNC[Async Processing]
        end
        
        subgraph "Database Performance"
            INDEX[Database Indexes]
            PARTITION[Table Partitioning]
            REPLICA[Read Replicas]
        end
        
        subgraph "Infrastructure Performance"
            LOAD_BAL[Load Balancing]
            SCALE[Auto Scaling]
            MONITOR[Performance Monitoring]
        end
    end
```

### 12.2 Database Performance Optimization

#### 12.2.1 Indexing Strategy
```sql
-- Performance-critical indexes
CREATE INDEX CONCURRENTLY idx_experiments_project_created 
ON experiments(project_id, created_at DESC);

CREATE INDEX CONCURRENTLY idx_experiment_runs_experiment_metrics 
ON experiment_runs(experiment_id) 
INCLUDE (metrics, completed_at);

CREATE INDEX CONCURRENTLY idx_datasets_project_size 
ON datasets(project_id, file_size DESC);

CREATE INDEX CONCURRENTLY idx_agent_tasks_status_created 
ON agent_tasks(status, created_at) 
WHERE status IN ('pending', 'running');

-- Partial indexes for active records
CREATE INDEX CONCURRENTLY idx_users_active 
ON users(email) 
WHERE is_active = true;

CREATE INDEX CONCURRENTLY idx_experiments_active 
ON experiments(project_id, updated_at DESC) 
WHERE status IN ('created', 'running');

-- Composite indexes for common queries
CREATE INDEX CONCURRENTLY idx_project_members_user_role 
ON project_members(user_id, project_id, role);

-- Full-text search indexes
CREATE INDEX CONCURRENTLY idx_experiments_fts 
ON experiments 
USING GIN(to_tsvector('english', name || ' ' || COALESCE(description, '')));
```

#### 12.2.2 Query Optimization
```python
# Optimized database queries with proper indexing and pagination
class OptimizedQueries:
    
    @staticmethod
    def get_user_experiments_paginated(
        db: Session, 
        user_id: str, 
        project_id: str = None,
        skip: int = 0, 
        limit: int = 100
    ):
        """Optimized query for user experiments with pagination"""
        query = db.query(Experiment).join(
            ProjectMember, 
            Experiment.project_id == ProjectMember.project_id
        ).filter(
            ProjectMember.user_id == user_id
        )
        
        if project_id:
            query = query.filter(Experiment.project_id == project_id)
        
        # Use index on (project_id, created_at DESC)
        query = query.order_by(Experiment.created_at.desc())
        
        return query.offset(skip).limit(limit).all()
    
    @staticmethod
    def get_experiment_runs_with_metrics(
        db: Session, 
        experiment_id: str,
        limit: int = 50
    ):
        """Optimized query for experiment runs with metrics"""
        return db.query(ExperimentRun).filter(
            ExperimentRun.experiment_id == experiment_id
        ).options(
            # Load only necessary columns
            load_only(
                ExperimentRun.id,
                ExperimentRun.run_name,
                ExperimentRun.algorithm,
                ExperimentRun.metrics,
                ExperimentRun.completed_at
            )
        ).order_by(
            ExperimentRun.completed_at.desc()
        ).limit(limit).all()
    
    @staticmethod
    def get_dataset_summary_stats(db: Session, dataset_id: str):
        """Optimized query for dataset statistics"""
        return db.query(Dataset).filter(
            Dataset.id == dataset_id
        ).options(
            load_only(
                Dataset.id,
                Dataset.name,
                Dataset.row_count,
                Dataset.column_count,
                Dataset.summary_stats,
                Dataset.file_size
            )
        ).first()
```

#### 12.2.3 Connection Pooling Configuration
```python
# Database connection pooling configuration
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool

def create_database_engine(database_url: str):
    """Create optimized database engine with connection pooling"""
    return create_engine(
        database_url,
        poolclass=QueuePool,
        pool_size=20,  # Number of connections to maintain
        max_overflow=30,  # Additional connections allowed
        pool_pre_ping=True,  # Validate connections before use
        pool_recycle=3600,  # Recycle connections every hour
        echo=False,  # Set to True for SQL debugging
        connect_args={
            "options": "-c timezone=utc",
            "application_name": "ml_platform",
        }
    )

# Connection pool monitoring
def get_pool_status(engine):
    """Get connection pool status for monitoring"""
    pool = engine.pool
    return {
        "size": pool.size(),
        "checked_in": pool.checkedin(),
        "checked_out": pool.checkedout(),
        "overflow": pool.overflow(),
        "invalid": pool.invalid()
    }
```

### 12.3 Caching Strategy

#### 12.3.1 Redis Caching Implementation
```python
import redis
import json
import pickle
from typing import Any, Optional
from functools import wraps
import hashlib

class CacheService:
    def __init__(self, redis_url: str):
        self.redis = redis.from_url(redis_url, decode_responses=False)
        self.default_ttl = 3600  # 1 hour
    
    def _make_key(self, prefix: str, *args, **kwargs) -> str:
        """Generate cache key from function arguments"""
        key_data = f"{prefix}:{args}:{sorted(kwargs.items())}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        try:
            value = self.redis.get(key)
            if value:
                return pickle.loads(value)
        except Exception:
            pass
        return None
    
    async def set(self, key: str, value: Any, ttl: int = None) -> bool:
        """Set value in cache"""
        try:
            ttl = ttl or self.default_ttl
            serialized = pickle.dumps(value)
            return self.redis.setex(key, ttl, serialized)
        except Exception:
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete value from cache"""
        try:
            return bool(self.redis.delete(key))
        except Exception:
            return False
    
    def cache_result(self, prefix: str, ttl: int = None):
        """Decorator to cache function results"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                cache_key = self._make_key(prefix, *args, **kwargs)
                
                # Try to get from cache
                cached_result = await self.get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # Execute function and cache result
                result = await func(*args, **kwargs)
                await self.set(cache_key, result, ttl)
                return result
            
            return wrapper
        return decorator

# Usage examples
cache_service = CacheService(settings.REDIS_URL)

@cache_service.cache_result("dataset_summary", ttl=1800)  # 30 minutes
async def get_dataset_summary(dataset_id: str):
    """Cached dataset summary retrieval"""
    # Expensive computation here
    pass

@cache_service.cache_result("experiment_metrics", ttl=300)  # 5 minutes
async def get_experiment_metrics(experiment_id: str):
    """Cached experiment metrics retrieval"""
    # Database query here
    pass
```

#### 12.3.2 Application-Level Caching
```python
from functools import lru_cache
from typing import Dict, List
import asyncio

class ApplicationCache:
    """In-memory application cache for frequently accessed data"""
    
    def __init__(self):
        self._user_permissions_cache = {}
        self._project_members_cache = {}
        self._cache_lock = asyncio.Lock()
    
    @lru_cache(maxsize=1000)
    def get_algorithm_config(self, algorithm_name: str) -> Dict:
        """Cache algorithm configurations"""
        # This would load from a configuration file or database
        algorithm_configs = {
            'RandomForestClassifier': {
                'param_grid': {
                    'n_estimators': [100, 200, 300],
                    'max_depth': [10, 20, None],
                    'min_samples_split': [2, 5, 10]
                },
                'default_params': {'n_estimators': 100, 'max_depth': 10}
            },
            'LogisticRegression': {
                'param_grid': {
                    'C': [0.1, 1.0, 10.0],
                    'solver': ['liblinear', 'lbfgs']
                },
                'default_params': {'C': 1.0, 'solver': 'lbfgs'}
            }
        }
        return algorithm_configs.get(algorithm_name, {})
    
    async def get_user_permissions(self, user_id: str, project_id: str) -> List[str]:
        """Cache user permissions for projects"""
        cache_key = f"{user_id}:{project_id}"
        
        async with self._cache_lock:
            if cache_key in self._user_permissions_cache:
                return self._user_permissions_cache[cache_key]
            
            # Load from database
            permissions = await self._load_user_permissions(user_id, project_id)
            self._user_permissions_cache[cache_key] = permissions
            
            # Schedule cache cleanup
            asyncio.create_task(self._cleanup_permissions_cache(cache_key))
            
            return permissions
    
    async def _cleanup_permissions_cache(self, cache_key: str):
        """Clean up permissions cache after timeout"""
        await asyncio.sleep(300)  # 5 minutes
        async with self._cache_lock:
            self._user_permissions_cache.pop(cache_key, None)
    
    async def invalidate_user_permissions(self, user_id: str):
        """Invalidate all cached permissions for a user"""
        async with self._cache_lock:
            keys_to_remove = [
                key for key in self._user_permissions_cache.keys()
                if key.startswith(f"{user_id}:")
            ]
            for key in keys_to_remove:
                self._user_permissions_cache.pop(key, None)

# Global cache instance
app_cache = ApplicationCache()
```

### 12.4 Asynchronous Processing

#### 12.4.1 Celery Task Queue Configuration
```python
# backend/app/core/celery_app.py
from celery import Celery
from app.core.config import settings

celery_app = Celery(
    "ml_platform",
    broker=settings.REDIS_URL,
    backend=settings.REDIS_URL,
    include=["app.tasks"]
)

# Celery configuration
celery_app.conf.update(
    task_serializer="pickle",
    accept_content=["pickle", "json"],
    result_serializer="pickle",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    result_expires=3600,  # 1 hour
)

# Task routing
celery_app.conf.task_routes = {
    "app.tasks.data_processing.*": {"queue": "data_processing"},
    "app.tasks.model_training.*": {"queue": "model_training"},
    "app.tasks.agent_tasks.*": {"queue": "agent_tasks"},
}
```

#### 12.4.2 Background Task Implementation
```python
# backend/app/tasks/data_processing.py
from celery import current_task
import pandas as pd
from app.core.celery_app import celery_app
from app.services.dataset_service import DatasetService

@celery_app.task(bind=True)
def process_dataset_upload(self, dataset_id: str, file_path: str):
    """Process uploaded dataset in background"""
    try:
        # Update task status
        current_task.update_state(
            state='PROGRESS',
            meta={'current': 0, 'total': 100, 'status': 'Loading dataset...'}
        )
        
        # Load dataset
        df = pd.read_csv(file_path)
        
        current_task.update_state(
            state='PROGRESS',
            meta={'current': 25, 'total': 100, 'status': 'Analyzing data quality...'}
        )
        
        # Analyze data quality
        quality_report = analyze_data_quality(df)
        
        current_task.update_state(
            state='PROGRESS',
            meta={'current': 50, 'total': 100, 'status': 'Generating statistics...'}
        )
        
        # Generate summary statistics
        summary_stats = generate_summary_statistics(df)
        
        current_task.update_state(
            state='PROGRESS',
            meta={'current': 75, 'total': 100, 'status': 'Updating database...'}
        )
        
        # Update database
        dataset_service = DatasetService()
        dataset_service.update_dataset_analysis(
            dataset_id, 
            quality_report, 
            summary_stats
        )
        
        current_task.update_state(
            state='SUCCESS',
            meta={'current': 100, 'total': 100, 'status': 'Processing complete'}
        )
        
        return {
            'status': 'completed',
            'dataset_id': dataset_id,
            'quality_report': quality_report,
            'summary_stats': summary_stats
        }
        
    except Exception as exc:
        current_task.update_state(
            state='FAILURE',
            meta={'error': str(exc)}
        )
        raise

@celery_app.task(bind=True)
def train_model_async(self, experiment_id: str, model_config: dict):
    """Train ML model asynchronously"""
    try:
        from app.services.model_service import ModelService
        
        model_service = ModelService()
        
        # Update progress
        current_task.update_state(
            state='PROGRESS',
            meta={'current': 0, 'total': 100, 'status': 'Preparing data...'}
        )
        
        # Prepare training data
        X_train, X_test, y_train, y_test = model_service.prepare_training_data(
            experiment_id
        )
        
        current_task.update_state(
            state='PROGRESS',
            meta={'current': 20, 'total': 100, 'status': 'Training model...'}
        )
        
        # Train model
        model, metrics = model_service.train_model(
            X_train, y_train, X_test, y_test, model_config
        )
        
        current_task.update_state(
            state='PROGRESS',
            meta={'current': 80, 'total': 100, 'status': 'Saving results...'}
        )
        
        # Save model and results
        model_service.save_model_results(experiment_id, model, metrics)
        
        return {
            'status': 'completed',
            'experiment_id': experiment_id,
            'metrics': metrics
        }
        
    except Exception as exc:
        current_task.update_state(
            state='FAILURE',
            meta={'error': str(exc)}
        )
        raise
```

### 12.5 Frontend Performance Optimization

#### 12.5.1 Next.js Performance Configuration
```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  
  // Image optimization
  images: {
    domains: ['localhost', 'api.mlplatform.com'],
    formats: ['image/webp', 'image/avif'],
  },
  
  // Compression
  compress: true,
  
  // Bundle analyzer (development only)
  ...(process.env.ANALYZE === 'true' && {
    webpack: (config, { isServer }) => {
      if (!isServer) {
        const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
        config.plugins.push(
          new BundleAnalyzerPlugin({
            analyzerMode: 'static',
            openAnalyzer: false,
          })
        );
      }
      return config;
    },
  }),
  
  // Performance optimizations
  swcMinify: true,
  
  // Headers for caching
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=300, stale-while-revalidate=60',
          },
        ],
      },
      {
        source: '/_next/static/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
```

#### 12.5.2 Component Performance Optimization
```typescript
// components/ExperimentList.tsx
import React, { memo, useMemo, useCallback } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
import { useInfiniteQuery } from '@tanstack/react-query';

interface ExperimentListProps {
  projectId: string;
}

const ExperimentList = memo(({ projectId }: ExperimentListProps) => {
  // Infinite query for large datasets
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteQuery({
    queryKey: ['experiments', projectId],
    queryFn: ({ pageParam = 0 }) =>
      fetchExperiments(projectId, pageParam),
    getNextPageParam: (lastPage, pages) =>
      lastPage.hasMore ? pages.length : undefined,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Flatten data for virtualization
  const experiments = useMemo(
    () => data?.pages.flatMap(page => page.experiments) ?? [],
    [data]
  );

  // Virtual scrolling for performance
  const parentRef = React.useRef<HTMLDivElement>(null);
  const virtualizer = useVirtualizer({
    count: experiments.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 100,
    overscan: 5,
  });

  // Memoized row renderer
  const renderRow = useCallback((index: number) => {
    const experiment = experiments[index];
    return (
      <ExperimentRow
        key={experiment.id}
        experiment={experiment}
        style={{
          height: `${virtualizer.getVirtualItems()[index]?.size}px`,
          transform: `translateY(${virtualizer.getVirtualItems()[index]?.start}px)`,
        }}
      />
    );
  }, [experiments, virtualizer]);

  return (
    <div
      ref={parentRef}
      className="h-96 overflow-auto"
    >
      <div
        style={{
          height: `${virtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative',
        }}
      >
        {virtualizer.getVirtualItems().map((virtualRow) => (
          <div
            key={virtualRow.index}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: `${virtualRow.size}px`,
              transform: `translateY(${virtualRow.start}px)`,
            }}
          >
            {renderRow(virtualRow.index)}
          </div>
        ))}
      </div>
    </div>
  );
});

ExperimentList.displayName = 'ExperimentList';

// Memoized row component
const ExperimentRow = memo(({ experiment, style }: {
  experiment: Experiment;
  style: React.CSSProperties;
}) => {
  return (
    <div style={style} className="border-b p-4">
      <h3 className="font-semibold">{experiment.name}</h3>
      <p className="text-sm text-gray-600">{experiment.description}</p>
      <div className="flex justify-between mt-2">
        <span className="text-xs text-gray-500">
          {experiment.created_at}
        </span>
        <span className={`px-2 py-1 rounded text-xs ${
          experiment.status === 'completed' 
            ? 'bg-green-100 text-green-800'
            : 'bg-yellow-100 text-yellow-800'
        }`}>
          {experiment.status}
        </span>
      </div>
    </div>
  );
});

ExperimentRow.displayName = 'ExperimentRow';

export default ExperimentList;
```

### 12.6 Scalability Planning

#### 12.6.1 Horizontal Scaling Configuration
```yaml
# docker-compose.scale.yml
version: '3.8'

services:
  # Load balancer
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.scale.conf:/etc/nginx/nginx.conf
    depends_on:
      - api
    deploy:
      replicas: 1

  # Scalable API service
  api:
    build: ./backend
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
    depends_on:
      - postgres
      - redis
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3

  # Scalable worker service
  celery-worker:
    build: ./backend
    command: celery -A app.core.celery_app worker --loglevel=info
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
    depends_on:
      - postgres
      - redis
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  # Database with read replicas
  postgres:
    image: postgres:16
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  postgres-replica:
    image: postgres:16
    environment:
      - PGUSER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - PGPASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_MASTER_SERVICE=postgres
    command: |
      bash -c "
      until pg_basebackup -h postgres -D /var/lib/postgresql/data -U ${POSTGRES_USER} -v -P -W; do
        echo 'Waiting for master to connect...'
        sleep 1s
      done
      echo 'standby_mode = on' >> /var/lib/postgresql/data/recovery.conf
      echo 'primary_conninfo = host=postgres port=5432 user=${POSTGRES_USER}' >> /var/lib/postgresql/data/recovery.conf
      postgres
      "
    depends_on:
      - postgres
    deploy:
      replicas: 1

volumes:
  postgres_data:
```

#### 12.6.2 Auto-scaling Configuration
```yaml
# kubernetes/api-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ml-platform-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ml-platform-api
  template:
    metadata:
      labels:
        app: ml-platform-api
    spec:
      containers:
      - name: api
        image: ml-platform-api:latest
        ports:
        - containerPort: 8000
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: ml-platform-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: ml-platform-secrets
              key: redis-url

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ml-platform-api-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ml-platform-api
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

---

## 13. Future Roadmap

### 13.1 Short-term Roadmap (3-6 months)

#### 13.1.1 Core Platform Enhancements
- **Advanced Data Visualization**: Interactive plots with Plotly.js integration
- **Real-time Collaboration**: WebSocket-based real-time experiment updates
- **Enhanced AI Agents**: More sophisticated AutoML capabilities
- **Model Deployment**: One-click model deployment to staging environments
- **Advanced Search**: Full-text search across experiments and datasets

#### 13.1.2 User Experience Improvements
- **Mobile Responsive Design**: Optimized mobile interface
- **Dark Mode Support**: Theme switching capability
- **Keyboard Shortcuts**: Power user keyboard navigation
- **Drag-and-Drop Interface**: Enhanced file upload and experiment creation
- **Notification System**: Real-time notifications for experiment completion

#### 13.1.3 Performance Optimizations
- **Database Query Optimization**: Advanced indexing and query tuning
- **Caching Layer Enhancement**: Multi-level caching strategy
- **Background Processing**: Improved task queue management
- **Frontend Bundle Optimization**: Code splitting and lazy loading

### 13.2 Medium-term Roadmap (6-12 months)

#### 13.2.1 Advanced ML Capabilities
- **Deep Learning Support**: Integration with TensorFlow and PyTorch
- **Computer Vision Agents**: Specialized agents for image processing tasks
- **Natural Language Processing**: NLP-specific preprocessing and modeling
- **Time Series Analysis**: Dedicated time series forecasting capabilities
- **Ensemble Methods**: Advanced ensemble and stacking techniques

#### 13.2.2 Enterprise Features
- **Single Sign-On (SSO)**: SAML and OAuth integration
- **Advanced RBAC**: Fine-grained permission system
- **Audit Logging**: Comprehensive audit trail
- **Data Governance**: Data lineage and compliance features
- **Multi-tenancy**: Organization-level isolation

#### 13.2.3 Integration Expansions
- **Cloud Storage**: AWS S3, Google Cloud Storage, Azure Blob integration
- **Data Sources**: Direct database connections (MySQL, MongoDB, etc.)
- **BI Tools**: Tableau, Power BI integration
- **Version Control**: Git integration for experiment versioning
- **Slack/Teams**: Notification integrations

### 13.3 Long-term Roadmap (12+ months)

#### 13.3.1 Advanced AI and Automation
- **Automated Feature Engineering**: AI-powered feature discovery
- **Neural Architecture Search**: Automated deep learning model design
- **Hyperparameter Optimization**: Advanced Bayesian optimization
- **Automated Model Selection**: Intelligent algorithm recommendation
- **Explainable AI**: Advanced model interpretability features

#### 13.3.2 Scalability and Performance
- **Kubernetes Native**: Full Kubernetes deployment support
- **Microservices Expansion**: Further service decomposition
- **Edge Computing**: Edge deployment capabilities
- **Multi-cloud Support**: Deployment across multiple cloud providers
- **Global CDN**: Worldwide content delivery network

#### 13.3.3 Advanced Analytics
- **Experiment Analytics**: Advanced experiment performance analytics
- **Resource Usage Analytics**: Detailed resource consumption tracking
- **Predictive Analytics**: Predict experiment success rates
- **A/B Testing**: Built-in A/B testing for model comparison
- **Business Intelligence**: Executive dashboards and reporting

### 13.4 Technology Evolution

#### 13.4.1 Frontend Technology Upgrades
- **React 19+**: Latest React features and optimizations
- **WebAssembly**: High-performance client-side computations
- **Progressive Web App**: Offline capabilities and app-like experience
- **WebGL Visualizations**: Advanced 3D data visualizations
- **Voice Interface**: Voice commands for accessibility

#### 13.4.2 Backend Technology Enhancements
- **Python 3.12+**: Latest Python features and performance improvements
- **Async Database Drivers**: Full async/await database operations
- **GraphQL API**: Alternative API interface for complex queries
- **gRPC Services**: High-performance inter-service communication
- **Event Sourcing**: Event-driven architecture implementation

#### 13.4.3 Infrastructure Modernization
- **Service Mesh**: Istio or Linkerd for microservices communication
- **Observability**: OpenTelemetry for distributed tracing
- **GitOps**: ArgoCD for declarative deployments
- **Infrastructure as Code**: Terraform for infrastructure management
- **Chaos Engineering**: Automated resilience testing

### 13.5 Research and Innovation

#### 13.5.1 Emerging ML Technologies
- **Federated Learning**: Distributed model training capabilities
- **MLOps Automation**: Advanced CI/CD for ML pipelines
- **Model Monitoring**: Drift detection and model health monitoring
- **Automated Data Quality**: AI-powered data quality assessment
- **Transfer Learning**: Pre-trained model fine-tuning capabilities

#### 13.5.2 Platform Intelligence
- **Usage Analytics**: AI-powered usage pattern analysis
- **Recommendation Engine**: Personalized experiment recommendations
- **Anomaly Detection**: Automated detection of unusual patterns
- **Performance Prediction**: Predict experiment resource requirements
- **Smart Notifications**: Intelligent notification prioritization

### 13.6 Community and Ecosystem

#### 13.6.1 Open Source Strategy
- **Plugin Architecture**: Third-party plugin development framework
- **API Extensions**: Extensible API for custom integrations
- **Community Contributions**: Open source components and contributions
- **Documentation Portal**: Comprehensive developer documentation
- **Example Gallery**: Community-contributed experiment examples

#### 13.6.2 Educational Features
- **Tutorial System**: Interactive tutorials for new users
- **Best Practices Guide**: ML best practices integration
- **Template Library**: Pre-built experiment templates
- **Learning Paths**: Guided learning experiences
- **Certification Program**: Platform proficiency certification

### 13.7 Success Metrics and KPIs

#### 13.7.1 Platform Adoption Metrics
- **User Growth**: Monthly active users and retention rates
- **Experiment Volume**: Number of experiments created and completed
- **Data Processing**: Volume of data processed through the platform
- **Model Deployment**: Number of models deployed to production
- **API Usage**: API call volume and response times

#### 13.7.2 Performance Metrics
- **System Uptime**: Platform availability and reliability
- **Response Times**: API and UI response time percentiles
- **Resource Utilization**: Efficient use of computational resources
- **Error Rates**: System error rates and resolution times
- **User Satisfaction**: User feedback and satisfaction scores

#### 13.7.3 Business Impact Metrics
- **Time to Insight**: Reduction in experiment completion time
- **Model Accuracy**: Improvement in model performance metrics
- **Collaboration Efficiency**: Team productivity improvements
- **Cost Optimization**: Reduction in ML infrastructure costs
- **Innovation Rate**: Increase in successful ML project delivery

---

## Conclusion

This comprehensive software specification document provides a detailed blueprint for building a scalable, production-ready ML experiment platform. The architecture leverages modern technologies including Docker containerization, microservices design, and industry-standard ML tools like MLflow and Neptune.AI.

The platform is designed to support the specified requirements of 5 concurrent users, 8GB datasets, and hundreds of experiments while providing room for future growth through horizontal scaling and performance optimizations. The AI agent system provides automated assistance for common ML tasks, reducing manual effort and improving experiment efficiency.

Key architectural decisions include:
- **Microservices Architecture**: Enabling independent scaling and maintenance
- **Docker Containerization**: Ensuring consistent deployment across environments
- **JWT Authentication**: Providing secure, stateless authentication
- **PostgreSQL Database**: Offering robust data persistence with ACID compliance
- **Redis Caching**: Improving performance through intelligent caching strategies
- **Comprehensive CI/CD**: Automating testing, building, and deployment processes

The roadmap outlines a clear path for evolution, from immediate enhancements to long-term advanced AI capabilities and enterprise features. This specification serves as a foundation for development teams to build a robust, scalable ML experimentation platform that meets current needs while accommodating future growth and technological advancement.

---

**Document Version**: 1.0  
**Last Updated**: June 15, 2025  
**Total Pages**: 87  
**Word Count**: ~45,000 words

---

*This specification document incorporates best practices from industry research including MLflow architecture patterns, Neptune.AI integration strategies, Node.js JWT authentication security practices, and GitHub Actions CI/CD deployment workflows as referenced in the research sources.*