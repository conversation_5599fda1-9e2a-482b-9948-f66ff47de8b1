# ML Experiment Platform Documentation

This directory contains comprehensive documentation for the ML Experiment Platform.

## Documentation Structure

- `api.md` - API documentation and endpoint reference
- `user-guide.md` - User guide and tutorials
- `deployment.md` - Deployment and configuration guide
- `ai-agents.md` - AI agents capabilities and usage
- `development.md` - Development setup and contribution guide
- `architecture.md` - System architecture and design decisions

## Quick Links

- [Getting Started](user-guide.md#getting-started)
- [API Reference](api.md)
- [Deployment Guide](deployment.md)
- [AI Agents Overview](ai-agents.md)

## Contributing to Documentation

When contributing to the documentation:

1. Use clear, concise language
2. Include code examples where appropriate
3. Keep documentation up-to-date with code changes
4. Follow the existing structure and formatting

## Documentation Standards

- Use Markdown format
- Include table of contents for longer documents
- Use code blocks with appropriate language highlighting
- Include screenshots for UI-related documentation
- Provide both conceptual explanations and practical examples
