"""
Configuration settings for AI Agents
"""

from typing import Optional, List
from pydantic import BaseSettings
import os


class AgentSettings(BaseSettings):
    """AI Agent configuration settings"""
    
    # Platform Integration
    PLATFORM_API_URL: str = "http://localhost:8000"
    PLATFORM_API_KEY: Optional[str] = None
    
    # Redis Configuration
    REDIS_URL: str = "redis://localhost:6379/1"
    REDIS_PASSWORD: Optional[str] = None
    
    # MLflow Configuration
    MLFLOW_TRACKING_URI: str = "http://localhost:5000"
    
    # Agent Configuration
    AGENT_NAME: str = "generic-agent"
    AGENT_VERSION: str = "1.0.0"
    MAX_CONCURRENT_TASKS: int = 3
    TASK_TIMEOUT_MINUTES: int = 60
    HEARTBEAT_INTERVAL_SECONDS: int = 30
    
    # ML Configuration
    DEFAULT_TEST_SIZE: float = 0.2
    DEFAULT_RANDOM_STATE: int = 42
    DEFAULT_CV_FOLDS: int = 5
    MAX_FEATURES_FOR_SELECTION: int = 1000
    
    # Data Processing Configuration
    MAX_DATASET_SIZE_MB: int = 1000
    SUPPORTED_FILE_FORMATS: List[str] = ["csv", "parquet", "json", "xlsx"]
    MAX_MISSING_VALUE_RATIO: float = 0.5
    
    # Model Configuration
    DEFAULT_ALGORITHMS: List[str] = [
        "RandomForestClassifier",
        "RandomForestRegressor",
        "LogisticRegression",
        "LinearRegression",
        "GradientBoostingClassifier",
        "GradientBoostingRegressor"
    ]
    
    # Hyperparameter Tuning Configuration
    HYPERPARAMETER_TUNING_TRIALS: int = 50
    HYPERPARAMETER_TUNING_TIMEOUT_MINUTES: int = 30
    
    # Feature Engineering Configuration
    MAX_POLYNOMIAL_DEGREE: int = 3
    MAX_INTERACTION_FEATURES: int = 100
    
    # Evaluation Configuration
    CLASSIFICATION_METRICS: List[str] = [
        "accuracy", "precision", "recall", "f1", "roc_auc"
    ]
    REGRESSION_METRICS: List[str] = [
        "mse", "rmse", "mae", "r2"
    ]
    
    # Logging Configuration
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"
    
    # Environment
    ENVIRONMENT: str = "development"
    DEBUG: bool = False
    
    class Config:
        env_file = ".env"
        case_sensitive = True


class DataPrepAgentSettings(AgentSettings):
    """Data Preparation Agent specific settings"""
    
    AGENT_NAME: str = "data-prep-agent"
    
    # Data cleaning thresholds
    OUTLIER_DETECTION_METHOD: str = "iqr"  # iqr, zscore, isolation_forest
    OUTLIER_THRESHOLD: float = 3.0
    
    # Missing value handling
    MISSING_VALUE_STRATEGIES: List[str] = ["drop", "mean", "median", "mode", "forward_fill"]
    DEFAULT_MISSING_VALUE_STRATEGY: str = "median"
    
    # Data type inference
    AUTO_INFER_TYPES: bool = True
    DATE_FORMATS: List[str] = ["%Y-%m-%d", "%d/%m/%Y", "%m/%d/%Y"]


class FeatureEngineeringAgentSettings(AgentSettings):
    """Feature Engineering Agent specific settings"""
    
    AGENT_NAME: str = "feature-engineering-agent"
    
    # Feature selection methods
    FEATURE_SELECTION_METHODS: List[str] = [
        "correlation", "mutual_info", "rfe", "lasso", "tree_importance"
    ]
    DEFAULT_FEATURE_SELECTION_METHOD: str = "correlation"
    
    # Scaling methods
    SCALING_METHODS: List[str] = ["standard", "minmax", "robust", "quantile"]
    DEFAULT_SCALING_METHOD: str = "standard"
    
    # Encoding methods
    CATEGORICAL_ENCODING_METHODS: List[str] = [
        "onehot", "label", "target", "binary", "ordinal"
    ]
    DEFAULT_CATEGORICAL_ENCODING: str = "onehot"


class ModelBuildingAgentSettings(AgentSettings):
    """Model Building Agent specific settings"""
    
    AGENT_NAME: str = "model-building-agent"
    
    # AutoML configuration
    AUTOML_ENABLED: bool = True
    AUTOML_TIME_BUDGET_MINUTES: int = 30
    
    # Model selection
    AUTO_MODEL_SELECTION: bool = True
    ENSEMBLE_METHODS: List[str] = ["voting", "stacking", "bagging"]
    
    # Cross-validation
    CV_STRATEGY: str = "stratified"  # stratified, kfold, time_series
    
    # Early stopping
    EARLY_STOPPING_ENABLED: bool = True
    EARLY_STOPPING_PATIENCE: int = 10


class EvaluationAgentSettings(AgentSettings):
    """Evaluation Agent specific settings"""
    
    AGENT_NAME: str = "evaluation-agent"
    
    # Statistical testing
    STATISTICAL_TESTS_ENABLED: bool = True
    SIGNIFICANCE_LEVEL: float = 0.05
    
    # Bias analysis
    BIAS_ANALYSIS_ENABLED: bool = True
    FAIRNESS_METRICS: List[str] = [
        "demographic_parity", "equalized_odds", "calibration"
    ]
    
    # Model interpretability
    INTERPRETABILITY_ENABLED: bool = True
    SHAP_ENABLED: bool = True
    LIME_ENABLED: bool = True
    
    # Performance benchmarking
    BENCHMARK_AGAINST_BASELINE: bool = True
    BASELINE_MODELS: List[str] = ["dummy", "linear", "tree"]


# Create settings instances
agent_settings = AgentSettings()
data_prep_settings = DataPrepAgentSettings()
feature_eng_settings = FeatureEngineeringAgentSettings()
model_building_settings = ModelBuildingAgentSettings()
evaluation_settings = EvaluationAgentSettings()
