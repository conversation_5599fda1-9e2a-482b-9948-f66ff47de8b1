# ML Experiment Platform

A comprehensive, containerized solution designed to streamline machine learning experimentation workflows for data scientists, ML engineers, and researchers.

## 🚀 Features

- **Data Management**: Upload, summarize, and visualize datasets
- **Experiment Tracking**: Comprehensive logging and comparison of ML experiments
- **Project Management**: Organize experiments into logical project structures
- **AI Agents**: Automated assistance for data preparation, feature engineering, model building, and evaluation
- **Integration**: Seamless connection with MLflow for experiment tracking

## 🏗️ Architecture

The platform follows a microservices architecture with:
- **Frontend**: Next.js 14+ with React 18+ and TypeScript
- **Backend**: Python FastAPI with SQLAlchemy and Pydantic
- **Database**: PostgreSQL 16+ with Redis caching
- **Storage**: MinIO for object storage
- **ML Tracking**: MLflow with optional Neptune.AI integration
- **AI Agents**: Specialized microservices for ML automation

## 📋 Requirements

- Docker 24+
- Docker Compose
- Node.js 20+ (for local development)
- Python 3.11+ (for local development)

## 🚀 Quick Start

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd pathfinder-ml
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start development services**
   ```bash
   ./scripts/dev-setup.sh
   ```

4. **Access the application**
   - Frontend: http://localhost:3000
   - API: http://localhost:8000
   - MLflow: http://localhost:5000
   - MinIO Console: http://localhost:9001

### Production Deployment

```bash
./scripts/deploy-prod.sh
```

## 📁 Project Structure

```
pathfinder-ml/
├── frontend/                 # Next.js frontend application
├── backend/                  # FastAPI backend services
├── agents/                   # AI agent services
├── infrastructure/           # Docker and deployment configs
├── docs/                     # Documentation
├── scripts/                  # Utility scripts
└── tests/                    # Integration tests
```

## 🧪 Testing

```bash
# Run all tests
./scripts/run-tests.sh

# Run specific test suites
cd backend && pytest tests/
cd frontend && npm test
```

## 📚 Documentation

- [API Documentation](docs/api.md)
- [User Guide](docs/user-guide.md)
- [Deployment Guide](docs/deployment.md)
- [AI Agents Guide](docs/ai-agents.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions, please open an issue in the GitHub repository.
