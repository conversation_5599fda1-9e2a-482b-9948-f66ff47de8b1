"""
MinIO object storage configuration and utilities
"""

from minio import Minio
from minio.error import S3<PERSON>rror
import io
from typing import Optional, BinaryIO
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)


class MinIOClient:
    """MinIO client wrapper for object storage operations"""
    
    def __init__(self):
        self.client = Minio(
            settings.MINIO_ENDPOINT,
            access_key=settings.MINIO_ACCESS_KEY,
            secret_key=settings.MINIO_SECRET_KEY,
            secure=settings.MINIO_SECURE,
        )
        self.bucket_name = settings.MINIO_BUCKET_NAME
        self._ensure_bucket_exists()
    
    def _ensure_bucket_exists(self):
        """Ensure the bucket exists, create if it doesn't"""
        try:
            if not self.client.bucket_exists(self.bucket_name):
                self.client.make_bucket(self.bucket_name)
                logger.info(f"Created bucket: {self.bucket_name}")
        except S3Error as e:
            logger.error(f"Error creating bucket: {e}")
            raise
    
    async def upload_file(
        self, 
        object_name: str, 
        file_data: BinaryIO, 
        content_type: str = "application/octet-stream"
    ) -> bool:
        """
        Upload a file to MinIO
        
        Args:
            object_name: Name of the object in storage
            file_data: File data to upload
            content_type: MIME type of the file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get file size
            file_data.seek(0, 2)  # Seek to end
            file_size = file_data.tell()
            file_data.seek(0)  # Reset to beginning
            
            self.client.put_object(
                bucket_name=self.bucket_name,
                object_name=object_name,
                data=file_data,
                length=file_size,
                content_type=content_type,
            )
            logger.info(f"Successfully uploaded {object_name}")
            return True
        except S3Error as e:
            logger.error(f"Error uploading file {object_name}: {e}")
            return False
    
    async def download_file(self, object_name: str) -> Optional[bytes]:
        """
        Download a file from MinIO
        
        Args:
            object_name: Name of the object to download
            
        Returns:
            bytes: File content if successful, None otherwise
        """
        try:
            response = self.client.get_object(self.bucket_name, object_name)
            data = response.read()
            response.close()
            response.release_conn()
            return data
        except S3Error as e:
            logger.error(f"Error downloading file {object_name}: {e}")
            return None
    
    async def delete_file(self, object_name: str) -> bool:
        """
        Delete a file from MinIO
        
        Args:
            object_name: Name of the object to delete
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.client.remove_object(self.bucket_name, object_name)
            logger.info(f"Successfully deleted {object_name}")
            return True
        except S3Error as e:
            logger.error(f"Error deleting file {object_name}: {e}")
            return False
    
    async def file_exists(self, object_name: str) -> bool:
        """
        Check if a file exists in MinIO
        
        Args:
            object_name: Name of the object to check
            
        Returns:
            bool: True if file exists, False otherwise
        """
        try:
            self.client.stat_object(self.bucket_name, object_name)
            return True
        except S3Error:
            return False
    
    async def get_file_info(self, object_name: str) -> Optional[dict]:
        """
        Get file information from MinIO
        
        Args:
            object_name: Name of the object
            
        Returns:
            dict: File information if successful, None otherwise
        """
        try:
            stat = self.client.stat_object(self.bucket_name, object_name)
            return {
                "size": stat.size,
                "etag": stat.etag,
                "content_type": stat.content_type,
                "last_modified": stat.last_modified,
                "metadata": stat.metadata,
            }
        except S3Error as e:
            logger.error(f"Error getting file info for {object_name}: {e}")
            return None
    
    async def list_files(self, prefix: str = "") -> list:
        """
        List files in MinIO bucket
        
        Args:
            prefix: Prefix to filter files
            
        Returns:
            list: List of file names
        """
        try:
            objects = self.client.list_objects(
                self.bucket_name, 
                prefix=prefix, 
                recursive=True
            )
            return [obj.object_name for obj in objects]
        except S3Error as e:
            logger.error(f"Error listing files: {e}")
            return []
    
    async def check_connection(self) -> bool:
        """
        Check if MinIO connection is working
        
        Returns:
            bool: True if connection is working, False otherwise
        """
        try:
            self.client.bucket_exists(self.bucket_name)
            return True
        except Exception:
            return False


# Global MinIO client instance
minio_client = MinIOClient()


def get_minio_client() -> MinIOClient:
    """Get MinIO client instance"""
    return minio_client
