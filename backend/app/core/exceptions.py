"""
Custom exceptions for the ML Experiment Platform
"""

from typing import Any, Dict, Optional
from fastapi import HTTPException, status


class MLPlatformException(Exception):
    """Base exception for ML Platform"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(MLPlatformException):
    """Raised when data validation fails"""
    pass


class AuthenticationError(MLPlatformException):
    """Raised when authentication fails"""
    pass


class AuthorizationError(MLPlatformException):
    """Raised when authorization fails"""
    pass


class ResourceNotFoundError(MLPlatformException):
    """Raised when a requested resource is not found"""
    pass


class ResourceAlreadyExistsError(MLPlatformException):
    """Raised when trying to create a resource that already exists"""
    pass


class DatabaseError(MLPlatformException):
    """Raised when database operations fail"""
    pass


class StorageError(MLPlatformException):
    """Raised when storage operations fail"""
    pass


class MLflowError(MLPlatformException):
    """Raised when MLflow operations fail"""
    pass


class AgentError(MLPlatformException):
    """Raised when AI agent operations fail"""
    pass


class DataProcessingError(MLPlatformException):
    """Raised when data processing fails"""
    pass


class ModelError(MLPlatformException):
    """Raised when model operations fail"""
    pass


class ConfigurationError(MLPlatformException):
    """Raised when configuration is invalid"""
    pass


# HTTP Exception mappings
def create_http_exception(
    exception: MLPlatformException,
    status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR
) -> HTTPException:
    """
    Convert a platform exception to an HTTP exception
    
    Args:
        exception: Platform exception to convert
        status_code: HTTP status code to use
        
    Returns:
        HTTPException: Converted HTTP exception
    """
    return HTTPException(
        status_code=status_code,
        detail={
            "message": exception.message,
            "details": exception.details,
            "type": type(exception).__name__
        }
    )


def validation_error_to_http(exception: ValidationError) -> HTTPException:
    """Convert ValidationError to HTTP 400"""
    return create_http_exception(exception, status.HTTP_400_BAD_REQUEST)


def authentication_error_to_http(exception: AuthenticationError) -> HTTPException:
    """Convert AuthenticationError to HTTP 401"""
    return create_http_exception(exception, status.HTTP_401_UNAUTHORIZED)


def authorization_error_to_http(exception: AuthorizationError) -> HTTPException:
    """Convert AuthorizationError to HTTP 403"""
    return create_http_exception(exception, status.HTTP_403_FORBIDDEN)


def resource_not_found_error_to_http(exception: ResourceNotFoundError) -> HTTPException:
    """Convert ResourceNotFoundError to HTTP 404"""
    return create_http_exception(exception, status.HTTP_404_NOT_FOUND)


def resource_already_exists_error_to_http(exception: ResourceAlreadyExistsError) -> HTTPException:
    """Convert ResourceAlreadyExistsError to HTTP 409"""
    return create_http_exception(exception, status.HTTP_409_CONFLICT)


def database_error_to_http(exception: DatabaseError) -> HTTPException:
    """Convert DatabaseError to HTTP 500"""
    return create_http_exception(exception, status.HTTP_500_INTERNAL_SERVER_ERROR)


def storage_error_to_http(exception: StorageError) -> HTTPException:
    """Convert StorageError to HTTP 500"""
    return create_http_exception(exception, status.HTTP_500_INTERNAL_SERVER_ERROR)


def mlflow_error_to_http(exception: MLflowError) -> HTTPException:
    """Convert MLflowError to HTTP 500"""
    return create_http_exception(exception, status.HTTP_500_INTERNAL_SERVER_ERROR)


def agent_error_to_http(exception: AgentError) -> HTTPException:
    """Convert AgentError to HTTP 500"""
    return create_http_exception(exception, status.HTTP_500_INTERNAL_SERVER_ERROR)


def data_processing_error_to_http(exception: DataProcessingError) -> HTTPException:
    """Convert DataProcessingError to HTTP 422"""
    return create_http_exception(exception, status.HTTP_422_UNPROCESSABLE_ENTITY)


def model_error_to_http(exception: ModelError) -> HTTPException:
    """Convert ModelError to HTTP 422"""
    return create_http_exception(exception, status.HTTP_422_UNPROCESSABLE_ENTITY)


def configuration_error_to_http(exception: ConfigurationError) -> HTTPException:
    """Convert ConfigurationError to HTTP 500"""
    return create_http_exception(exception, status.HTTP_500_INTERNAL_SERVER_ERROR)


# Exception handler mapping
EXCEPTION_HANDLERS = {
    ValidationError: validation_error_to_http,
    AuthenticationError: authentication_error_to_http,
    AuthorizationError: authorization_error_to_http,
    ResourceNotFoundError: resource_not_found_error_to_http,
    ResourceAlreadyExistsError: resource_already_exists_error_to_http,
    DatabaseError: database_error_to_http,
    StorageError: storage_error_to_http,
    MLflowError: mlflow_error_to_http,
    AgentError: agent_error_to_http,
    DataProcessingError: data_processing_error_to_http,
    ModelError: model_error_to_http,
    ConfigurationError: configuration_error_to_http,
}
