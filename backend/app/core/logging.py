"""
Logging configuration for the ML Experiment Platform
"""

import logging
import sys
from typing import Dict, Any
import structlog
from pythonjsonlogger import jsonlogger

from app.core.config import settings


def setup_logging() -> None:
    """
    Set up structured logging for the application
    """
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, settings.LOG_LEVEL.upper()),
    )
    
    # Set up JSON formatter for production
    if settings.ENVIRONMENT == "production":
        formatter = jsonlogger.JsonFormatter(
            "%(asctime)s %(name)s %(levelname)s %(message)s"
        )
        
        handler = logging.StreamHandler(sys.stdout)
        handler.setFormatter(formatter)
        
        root_logger = logging.getLogger()
        root_logger.handlers = [handler]
        root_logger.setLevel(getattr(logging, settings.LOG_LEVEL.upper()))


def get_logger(name: str) -> structlog.BoundLogger:
    """
    Get a structured logger instance
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        structlog.BoundLogger: Configured logger instance
    """
    return structlog.get_logger(name)


class LoggerMixin:
    """Mixin class to add logging capabilities to any class"""
    
    @property
    def logger(self) -> structlog.BoundLogger:
        """Get logger for this class"""
        return get_logger(self.__class__.__name__)


def log_function_call(func_name: str, **kwargs) -> None:
    """
    Log a function call with parameters
    
    Args:
        func_name: Name of the function being called
        **kwargs: Function parameters to log
    """
    logger = get_logger("function_calls")
    logger.info(
        "Function called",
        function=func_name,
        parameters=kwargs
    )


def log_api_request(
    method: str,
    path: str,
    user_id: str = None,
    status_code: int = None,
    duration_ms: float = None,
    **kwargs
) -> None:
    """
    Log an API request
    
    Args:
        method: HTTP method
        path: Request path
        user_id: User ID if authenticated
        status_code: Response status code
        duration_ms: Request duration in milliseconds
        **kwargs: Additional context
    """
    logger = get_logger("api_requests")
    logger.info(
        "API request",
        method=method,
        path=path,
        user_id=user_id,
        status_code=status_code,
        duration_ms=duration_ms,
        **kwargs
    )


def log_database_operation(
    operation: str,
    table: str,
    duration_ms: float = None,
    affected_rows: int = None,
    **kwargs
) -> None:
    """
    Log a database operation
    
    Args:
        operation: Type of operation (SELECT, INSERT, UPDATE, DELETE)
        table: Database table name
        duration_ms: Operation duration in milliseconds
        affected_rows: Number of affected rows
        **kwargs: Additional context
    """
    logger = get_logger("database")
    logger.info(
        "Database operation",
        operation=operation,
        table=table,
        duration_ms=duration_ms,
        affected_rows=affected_rows,
        **kwargs
    )


def log_ml_operation(
    operation: str,
    experiment_id: str = None,
    model_name: str = None,
    duration_ms: float = None,
    **kwargs
) -> None:
    """
    Log a machine learning operation
    
    Args:
        operation: Type of ML operation
        experiment_id: Experiment ID if applicable
        model_name: Model name if applicable
        duration_ms: Operation duration in milliseconds
        **kwargs: Additional context
    """
    logger = get_logger("ml_operations")
    logger.info(
        "ML operation",
        operation=operation,
        experiment_id=experiment_id,
        model_name=model_name,
        duration_ms=duration_ms,
        **kwargs
    )


def log_agent_operation(
    agent_type: str,
    task_id: str,
    operation: str,
    status: str,
    duration_ms: float = None,
    **kwargs
) -> None:
    """
    Log an AI agent operation
    
    Args:
        agent_type: Type of AI agent
        task_id: Task ID
        operation: Operation being performed
        status: Operation status
        duration_ms: Operation duration in milliseconds
        **kwargs: Additional context
    """
    logger = get_logger("ai_agents")
    logger.info(
        "Agent operation",
        agent_type=agent_type,
        task_id=task_id,
        operation=operation,
        status=status,
        duration_ms=duration_ms,
        **kwargs
    )


def log_error(
    error: Exception,
    context: Dict[str, Any] = None,
    **kwargs
) -> None:
    """
    Log an error with context
    
    Args:
        error: Exception that occurred
        context: Additional context information
        **kwargs: Additional fields
    """
    logger = get_logger("errors")
    logger.error(
        "Error occurred",
        error_type=type(error).__name__,
        error_message=str(error),
        context=context or {},
        **kwargs,
        exc_info=True
    )


# Initialize logging when module is imported
setup_logging()
