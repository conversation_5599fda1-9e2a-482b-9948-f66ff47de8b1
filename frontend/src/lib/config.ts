/**
 * Frontend configuration settings
 */

export const config = {
  // API Configuration
  apiUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
  apiVersion: 'v1',
  
  // Authentication
  authUrl: process.env.NEXTAUTH_URL || 'http://localhost:3000',
  
  // Application Settings
  appName: 'ML Experiment Platform',
  appVersion: '1.0.0',
  appDescription: 'A comprehensive ML experiment platform with AI agents',
  
  // UI Configuration
  theme: {
    defaultMode: 'light' as 'light' | 'dark',
    colors: {
      primary: '#3b82f6',
      secondary: '#64748b',
      success: '#22c55e',
      warning: '#f59e0b',
      error: '#ef4444',
    },
  },
  
  // Data Visualization
  charts: {
    defaultHeight: 400,
    defaultWidth: 600,
    colorPalette: [
      '#3b82f6', '#ef4444', '#22c55e', '#f59e0b', '#8b5cf6',
      '#06b6d4', '#f97316', '#84cc16', '#ec4899', '#6366f1'
    ],
  },
  
  // File Upload
  upload: {
    maxFileSize: 100 * 1024 * 1024, // 100MB
    allowedTypes: [
      'text/csv',
      'application/json',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/x-parquet'
    ],
    allowedExtensions: ['.csv', '.json', '.xlsx', '.xls', '.parquet'],
  },
  
  // Pagination
  pagination: {
    defaultPageSize: 20,
    pageSizeOptions: [10, 20, 50, 100],
  },
  
  // Real-time Updates
  websocket: {
    reconnectInterval: 5000,
    maxReconnectAttempts: 10,
  },
  
  // Caching
  cache: {
    defaultTTL: 5 * 60 * 1000, // 5 minutes
    maxSize: 100, // Maximum number of cached items
  },
  
  // Development
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
  
  // Feature Flags
  features: {
    aiAgents: true,
    neptuneIntegration: false,
    advancedAnalytics: true,
    realTimeCollaboration: false,
  },
  
  // External Services
  external: {
    mlflowUrl: 'http://localhost:5000',
    docsUrl: '/docs',
    supportEmail: '<EMAIL>',
  },
  
  // Timeouts
  timeouts: {
    apiRequest: 30000, // 30 seconds
    fileUpload: 300000, // 5 minutes
    longRunningTask: 600000, // 10 minutes
  },
  
  // Validation
  validation: {
    minPasswordLength: 8,
    maxProjectNameLength: 100,
    maxDescriptionLength: 500,
    maxDatasetNameLength: 100,
  },
  
  // Experiment Configuration
  experiments: {
    defaultMetrics: ['accuracy', 'precision', 'recall', 'f1_score'],
    maxExperimentsPerProject: 1000,
    autoRefreshInterval: 30000, // 30 seconds
  },
  
  // AI Agents Configuration
  agents: {
    types: [
      { id: 'data_prep', name: 'Data Preparation', icon: '🧹' },
      { id: 'feature_eng', name: 'Feature Engineering', icon: '⚙️' },
      { id: 'model_building', name: 'Model Building', icon: '🤖' },
      { id: 'evaluation', name: 'Evaluation', icon: '📊' },
    ],
    statusRefreshInterval: 5000, // 5 seconds
  },
} as const;

// API endpoints
export const endpoints = {
  auth: {
    login: '/auth/login',
    register: '/auth/register',
    refresh: '/auth/refresh',
    logout: '/auth/logout',
  },
  projects: {
    list: '/projects',
    create: '/projects',
    get: (id: string) => `/projects/${id}`,
    update: (id: string) => `/projects/${id}`,
    delete: (id: string) => `/projects/${id}`,
  },
  datasets: {
    list: '/datasets',
    upload: '/datasets/upload',
    get: (id: string) => `/datasets/${id}`,
    summary: (id: string) => `/datasets/${id}/summary`,
    preview: (id: string) => `/datasets/${id}/preview`,
    delete: (id: string) => `/datasets/${id}`,
  },
  experiments: {
    list: '/experiments',
    create: '/experiments',
    get: (id: string) => `/experiments/${id}`,
    update: (id: string) => `/experiments/${id}`,
    delete: (id: string) => `/experiments/${id}`,
    runs: (id: string) => `/experiments/${id}/runs`,
  },
  agents: {
    status: '/agents/status',
    dataPrepStart: '/agents/data-prep',
    featureEngStart: '/agents/feature-engineering',
    modelBuildStart: '/agents/model-building',
    evaluationStart: '/agents/evaluation',
    taskStatus: (id: string) => `/agents/tasks/${id}`,
    taskLogs: (id: string) => `/agents/tasks/${id}/logs`,
  },
} as const;

// Helper functions
export const getApiUrl = (endpoint: string): string => {
  const baseUrl = config.apiUrl.endsWith('/') 
    ? config.apiUrl.slice(0, -1) 
    : config.apiUrl;
  const apiPath = `/api/${config.apiVersion}`;
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  
  return `${baseUrl}${apiPath}${cleanEndpoint}`;
};

export const isFeatureEnabled = (feature: keyof typeof config.features): boolean => {
  return config.features[feature];
};

export const getThemeColor = (color: keyof typeof config.theme.colors): string => {
  return config.theme.colors[color];
};
