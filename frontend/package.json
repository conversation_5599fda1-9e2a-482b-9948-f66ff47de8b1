{"name": "ml-experiment-platform-frontend", "version": "1.0.0", "description": "Frontend for ML Experiment Platform", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "analyze": "cross-env ANALYZE=true next build"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "@next/font": "^14.0.0", "next-auth": "^4.24.0", "@tanstack/react-query": "^5.0.0", "zustand": "^4.4.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "plotly.js": "^2.27.0", "react-plotly.js": "^2.6.0", "d3": "^7.8.0", "@types/d3": "^7.4.0", "react-hook-form": "^7.47.0", "@hookform/resolvers": "^3.3.0", "zod": "^3.22.0", "axios": "^1.6.0", "react-dropzone": "^14.2.0", "react-hot-toast": "^2.4.0", "lucide-react": "^0.292.0", "@headlessui/react": "^1.7.0", "@heroicons/react": "^2.0.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-plotly.js": "^2.6.0", "typescript": "^5.2.0", "eslint": "^8.52.0", "eslint-config-next": "^14.0.0", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0", "jest": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.0", "jest-environment-jsdom": "^29.7.0", "cross-env": "^7.0.3", "@next/bundle-analyzer": "^14.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}