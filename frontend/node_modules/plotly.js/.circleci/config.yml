version: 2.1
orbs:
  browser-tools: circleci/browser-tools@1.4.8

# Inspired by:
# https://github.com/CircleCI-Public/circleci-demo-workflows/blob/workspace-forwarding/.circleci/config.yml
# https://circleci.com/docs/2.0/workflows/#using-workspaces-to-share-data-among-jobs
#
# For list of official CircleCI node.js images, go to:
# https://hub.docker.com/r/cimg/node/tags/

jobs:
  install-and-cibuild:
    docker:
      - image: cimg/node:18.20.4
    working_directory: ~/plotly.js
    steps:
      - checkout
      - run:
          name: Set up build environment
          command: .circleci/env_build.sh
      - run:
          name: Pretest
          command: npm run pretest
      - run:
          name: CI-Build
          command: npm run cibuild
      - run:
          name: Delete git
          command: rm -rf .git
      - persist_to_workspace:
          root: /home/<USER>
          paths:
            - plotly.js

  timezone-jasmine:
    docker:
      # need '-browsers' version to test in real (xvfb-wrapped) browsers
      - image: cimg/node:18.20.4-browsers
    working_directory: ~/plotly.js
    steps:
      - run: sudo apt-get update
      - browser-tools/install-browser-tools:
          install-firefox: false
          install-geckodriver: false
          install-chrome: true
          chrome-version: "127.0.6533.119"
      - attach_workspace:
          at: ~/
      - run:
          name: Run hover_label test in UTC timezone
          environment:
            TZ: "UTC"
          command: date && npm run test-jasmine hover_label
      - run:
          name: Run hover_label test in Europe/Berlin timezone
          environment:
            TZ: "Europe/Berlin"
          command: date && npm run test-jasmine hover_label
      - run:
          name: Run hover_label test in Asia/Tokyo timezone
          environment:
            TZ: "Asia/Tokyo"
          command: date && npm run test-jasmine hover_label
      - run:
          name: Run hover_label test in America/Toronto timezone
          environment:
            TZ: "America/Toronto"
          command: date && npm run test-jasmine hover_label

  no-gl-jasmine:
    docker:
      # need '-browsers' version to test in real (xvfb-wrapped) browsers
      - image: cimg/node:18.20.4-browsers
    environment:
      # Alaska time (arbitrary timezone to test date logic)
      TZ: "America/Anchorage"
    parallelism: 12
    working_directory: ~/plotly.js
    steps:
      - run: sudo apt-get update
      - browser-tools/install-browser-tools:
          install-firefox: false
          install-geckodriver: false
          install-chrome: true
          chrome-version: "127.0.6533.119"
      - attach_workspace:
          at: ~/
      - run:
          name: Run jasmine tests (part A)
          command: .circleci/test.sh no-gl-jasmine

  webgl-jasmine:
    docker:
      # need '-browsers' version to test in real (xvfb-wrapped) browsers
      - image: cimg/node:18.20.4-browsers
    environment:
      # Alaska time (arbitrary timezone to test date logic)
      TZ: "America/Anchorage"
    parallelism: 8
    working_directory: ~/plotly.js
    steps:
      - run: sudo apt-get update
      - browser-tools/install-browser-tools:
          install-firefox: false
          install-geckodriver: false
          install-chrome: true
          chrome-version: "127.0.6533.119"
      - attach_workspace:
          at: ~/
      - run:
          name: Run jasmine tests (part B)
          command: .circleci/test.sh webgl-jasmine

  virtual-webgl-jasmine:
    docker:
      # need '-browsers' version to test in real (xvfb-wrapped) browsers
      - image: cimg/node:18.20.4-browsers
    environment:
      # Alaska time (arbitrary timezone to test date logic)
      TZ: "America/Anchorage"
    parallelism: 8
    working_directory: ~/plotly.js
    steps:
      - run: sudo apt-get update
      - browser-tools/install-browser-tools:
          install-firefox: false
          install-geckodriver: false
          install-chrome: true
          chrome-version: "127.0.6533.119"
      - attach_workspace:
          at: ~/
      - run:
          name: Run jasmine tests (part B)
          command: .circleci/test.sh virtual-webgl-jasmine

  webgl-jasmine-chromeLatest:
    docker:
      # need '-browsers' version to test in real (xvfb-wrapped) browsers
      - image: cimg/node:18.20.4-browsers
    environment:
      # Alaska time (arbitrary timezone to test date logic)
      TZ: "America/Anchorage"
    parallelism: 8
    working_directory: ~/plotly.js
    steps:
      - browser-tools/install-browser-tools: &browser-versions
          install-firefox: false
          install-geckodriver: false
          install-chrome: true
      - attach_workspace:
          at: ~/
      - run:
          name: Run jasmine tests (part B)
          command: .circleci/test.sh webgl-jasmine

  flaky-no-gl-jasmine:
    docker:
      # need '-browsers' version to test in real (xvfb-wrapped) browsers
      - image: cimg/node:18.20.4-browsers
    environment:
      # Alaska time (arbitrary timezone to test date logic)
      TZ: "America/Anchorage"
    working_directory: ~/plotly.js
    steps:
      - run: sudo apt-get update
      - browser-tools/install-browser-tools:
          install-firefox: false
          install-geckodriver: false
          install-chrome: true
          chrome-version: "127.0.6533.119"
      - attach_workspace:
          at: ~/
      - run:
          name: Run jasmine tests (part C)
          command: .circleci/test.sh flaky-no-gl-jasmine

  bundle-jasmine:
    docker:
      # need '-browsers' version to test in real (xvfb-wrapped) browsers
      - image: cimg/node:18.20.4-browsers
    environment:
      # Alaska time (arbitrary timezone to test date logic)
      TZ: "America/Anchorage"
    working_directory: ~/plotly.js
    steps:
      - run: sudo apt-get update
      - browser-tools/install-browser-tools:
          install-firefox: false
          install-geckodriver: false
          install-chrome: true
          chrome-version: "127.0.6533.119"
      - attach_workspace:
          at: ~/
      - run:
          name: Run jasmine tests (part D)
          command: .circleci/test.sh bundle-jasmine

  mathjax-firefoxLatest:
    docker:
      # need '-browsers' version to test in real (xvfb-wrapped) browsers
      - image: cimg/node:18.20.4-browsers
    environment:
      # Alaska time (arbitrary timezone to test date logic)
      TZ: "America/Anchorage"
    working_directory: ~/plotly.js
    steps:
      - browser-tools/install-browser-tools:
          install-chrome: false
          install-chromedriver: false
      - attach_workspace:
          at: ~/
      - run:
          name: Test MathJax on firefox-latest
          command: .circleci/test.sh mathjax-firefox

  make-baselines-virtual-webgl:
    parallelism: 8
    docker:
      - image: circleci/python:3.8.9
    working_directory: ~/plotly.js
    steps:
      - attach_workspace:
          at: ~/
      - run:
          name: Install kaleido, plotly.io and required fonts
          command: .circleci/env_image.sh
      - run:
          name: Create png files using virtual-webgl & WebGL v1
          command: .circleci/test.sh make-baselines-virtual-webgl
      - persist_to_workspace:
          root: ~/
          paths:
            - plotly.js

  make-baselines-mathjax3:
    docker:
      - image: circleci/python:3.8.9
    working_directory: ~/plotly.js
    steps:
      - attach_workspace:
          at: ~/
      - run:
          name: Install kaleido, plotly.io and required fonts
          command: .circleci/env_image.sh
      - run:
          name: Create mathjax v3 png files
          command: .circleci/test.sh make-baselines-mathjax3
      - persist_to_workspace:
          root: ~/
          paths:
            - plotly.js

  make-baselines:
    parallelism: 12
    docker:
      - image: circleci/python:3.8.9
    working_directory: ~/plotly.js
    steps:
      - attach_workspace:
          at: ~/
      - run:
          name: Install kaleido, plotly.io and required fonts
          command: .circleci/env_image.sh
      - run:
          name: Create all png files
          command: .circleci/test.sh make-baselines
      - persist_to_workspace:
          root: ~/
          paths:
            - plotly.js

  make-baselines-b64:
    parallelism: 12
    docker:
      - image: circleci/python:3.8.9
    working_directory: ~/plotly.js
    steps:
      - attach_workspace:
          at: ~/
      - run:
          name: Install kaleido, plotly.io and required fonts
          command: .circleci/env_image.sh
      - run:
          name: Create all png files
          command: .circleci/test.sh make-baselines-b64
      - persist_to_workspace:
          root: ~/
          paths:
            - plotly.js

  test-baselines:
    docker:
      - image: cimg/node:18.20.4
    working_directory: ~/plotly.js
    steps:
      - attach_workspace:
          at: ~/
      - run:
          name: Compare pixels
          command: .circleci/test.sh test-image ; find build -maxdepth 1 -type f -delete
      - store_artifacts:
          path: build
          destination: /

  test-baselines-virtual-webgl:
    docker:
      - image: cimg/node:18.20.4
    working_directory: ~/plotly.js
    steps:
      - attach_workspace:
          at: ~/
      - run:
          name: Compare pixels
          command: .circleci/test.sh test-image-virtual-webgl ; find build -maxdepth 1 -type f -delete
      - store_artifacts:
          path: build
          destination: /

  test-baselines-b64:
    docker:
      - image: cimg/node:18.20.4
    working_directory: ~/plotly.js
    steps:
      - attach_workspace:
          at: ~/
      - run:
          name: Compare pixels
          command: .circleci/test.sh test-image ; find build -maxdepth 1 -type f -delete
      - store_artifacts:
          path: build
          destination: /

  test-baselines-mathjax3:
    docker:
      - image: cimg/node:18.20.4
    working_directory: ~/plotly.js
    steps:
      - attach_workspace:
          at: ~/
      - run:
          name: Compare pixels of mathjax v3 baselines
          command: .circleci/test.sh test-image-mathjax3
      - store_artifacts:
          path: build
          destination: /

  make-exports:
    docker:
      - image: circleci/python:3.8.9
    working_directory: ~/plotly.js
    steps:
      - attach_workspace:
          at: ~/
      - run:
          name: Install kaleido, plotly.io and required fonts
          command: .circleci/env_image.sh
      - run:
          name: Install poppler-utils to have pdftops for exporting eps
          command: |
            sudo apt-get update --allow-releaseinfo-change
            sudo apt-get install poppler-utils
      - run:
          name: Create svg, jpg, jpeg, webp, pdf and eps files
          command: python3 test/image/make_exports.py
      - persist_to_workspace:
          root: ~/
          paths:
            - plotly.js

  test-exports:
    docker:
      - image: cimg/node:18.20.4
    working_directory: ~/plotly.js
    steps:
      - attach_workspace:
          at: ~/
      - run:
          name: Test export sizes
          command: node test/image/export_test.js ; find build -maxdepth 1 -type f -delete
      - store_artifacts:
          path: build
          destination: /

  mock-validation:
    docker:
      - image: cimg/node:18.20.4
    working_directory: ~/plotly.js
    steps:
      - attach_workspace:
          at: ~/
      - run:
          name: Test validation using node.js and jsdom
          command: npm run test-plain-obj
      - run:
          name: Validate mocks
          command: npm run test-mock

  source-syntax:
    docker:
      - image: cimg/node:18.20.4
    working_directory: ~/plotly.js
    steps:
      - attach_workspace:
          at: ~/
      - run:
          name: Run syntax tests on source files
          command: .circleci/test.sh source-syntax

  publish-dist:
    docker:
      - image: cimg/node:18.20.4
    working_directory: ~/plotly.js
    steps:
      - checkout
      - run:
          name: Set up build environment
          command: .circleci/env_build.sh
      - run:
          name: Preview CHANGELOG for next release (only on master)
          command: |
            if [ $CIRCLE_BRANCH == "master" ]
              then npm run use-draftlogs && git --no-pager diff --color-words CHANGELOG.md || true
            fi
      - run:
          name: Set draft version in package.json
          command: |
            node --eval "var fs = require('fs'); var inOut = './package.json'; var data = JSON.parse(fs.readFileSync(inOut)); var a = process.argv; data.version = a[a.length - 1].replace('v', ''); fs.writeFileSync(inOut, JSON.stringify(data, null, 2) + '\n');" `git describe`
      - run:
          name: View package.json diff between previous and next releases (including above draft version change)
          command: git --no-pager diff --color-words tags/$(git describe --tags --abbrev=0) package.json || true
      - run:
          name: Build dist/
          command: npm run build
      - store_artifacts:
          path: dist
          destination: dist
      - run:
          name: View dist/README.md diff between previous and next releases (including new bundle sizes)
          command: git --no-pager diff --color-words tags/$(git describe --tags --abbrev=0) dist/README.md || true
      - run:
          name: Preview plot-schema diff between previous and next releases (only on master)
          command: |
            if [ $CIRCLE_BRANCH == "master" ]
              then git --no-pager diff tags/$(git describe --tags --abbrev=0) dist/plot-schema.json || true
            fi
      - run:
          name: Pack tarball
          command: |
            npm pack
            version=$(node --eval "console.log(require('./package.json').version)")
            mv plotly.js-$version.tgz plotly.js.tgz
      - store_artifacts:
          path: plotly.js.tgz
          destination: /plotly.js.tgz
      - run:
          name: Show URLs to build files
          command: |
            PROJECT_NUM=45646037
            echo https://$CIRCLE_BUILD_NUM-$PROJECT_NUM-gh.circle-artifacts.com/0/plotly.js.tgz
            echo https://$CIRCLE_BUILD_NUM-$PROJECT_NUM-gh.circle-artifacts.com/0/dist/plotly.js
            echo https://$CIRCLE_BUILD_NUM-$PROJECT_NUM-gh.circle-artifacts.com/0/dist/plotly.min.js
            echo https://$CIRCLE_BUILD_NUM-$PROJECT_NUM-gh.circle-artifacts.com/0/dist/plot-schema.json
      - run:
          name: Test plot-schema.json diff - If failed, after (npm start) you could run (npm run schema && git add test/plot-schema.json && git commit -m "update plot-schema diff")
          command: diff --unified --color dist/plot-schema.json test/plot-schema.json
      - run:
          name: Test plotly.min.js import using amdefine
          command: npm run test-amdefine
      - run:
          name: Test plotly.min.js import using requirejs
          command: npm run test-requirejs

  test-stackgl-bundle:
    docker:
      - image: cimg/node:18.20.4
    working_directory: ~/plotly.js
    steps:
      - checkout
      - run:
          name: Set up build environment
          command: cd stackgl_modules && npm ci
      - run:
          name: Bundle bundle-stackgl/index.js
          command: cd stackgl_modules && cp index.js INDEX.js && npm run bundle-stackgl
      - run:
          name: Test stackgl_modules/index.js diff - If failed please remember this file in auto generated and you should not modify it directly until a dependeny change. To suggest changes please submit pull request to the relevant dependency.
          command: diff --unified --color stackgl_modules/INDEX.js stackgl_modules/index.js
      - store_artifacts:
          path: stackgl_modules/index.js
          destination: stackgl_modules/index.js

workflows:
  version: 2
  build-and-test:
    jobs:
      - install-and-cibuild
      - timezone-jasmine:
          requires:
            - install-and-cibuild
      - bundle-jasmine:
          requires:
            - install-and-cibuild
      - mathjax-firefoxLatest:
          requires:
            - install-and-cibuild
      - no-gl-jasmine:
          requires:
            - install-and-cibuild
      - webgl-jasmine:
          requires:
            - install-and-cibuild
      - virtual-webgl-jasmine:
          requires:
            - install-and-cibuild
      - webgl-jasmine-chromeLatest:
          requires:
            - install-and-cibuild
      - flaky-no-gl-jasmine:
          requires:
            - install-and-cibuild
      - make-baselines-virtual-webgl:
          requires:
            - install-and-cibuild
      - test-baselines-virtual-webgl:
          requires:
            - make-baselines-virtual-webgl
      - make-baselines-mathjax3:
          requires:
            - install-and-cibuild
      - test-baselines-mathjax3:
          requires:
            - make-baselines-mathjax3
      - make-baselines-b64:
          requires:
            - install-and-cibuild
      - test-baselines-b64:
          requires:
            - make-baselines-b64
      - make-baselines:
          requires:
            - install-and-cibuild
      - test-baselines:
          requires:
            - make-baselines
      - make-exports:
          requires:
            - install-and-cibuild
      - test-exports:
          requires:
            - make-exports
      - mock-validation:
          requires:
            - install-and-cibuild
      - source-syntax:
          requires:
            - install-and-cibuild

      - publish-dist

      - test-stackgl-bundle
