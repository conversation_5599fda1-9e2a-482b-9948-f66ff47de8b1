var locale={moduleType:"locale",name:"sk",dictionary:{Autoscale:"Auto rozsah","Box Select":"Obd\u013a\u017enikov\xfd v\xfdber","Click to enter Colorscale title":"Kliknite pre zadanie n\xe1zvu farebnej \u0161k\xe1ly","Click to enter Component A title":"Kliknite pre zadanie n\xe1zvu komponentu A","Click to enter Component B title":"Kliknite pre zadanie n\xe1zvu komponentu B","Click to enter Component C title":"Kliknite pre zadanie n\xe1zvu komponentu C","Click to enter Plot title":"Kliknite pre zadanie n\xe1zvu grafu","Click to enter X axis title":"Kliknite pre zadanie n\xe1zvu osi X","Click to enter Y axis title":"Kliknite pre zadanie n\xe1zvu osi Y","Click to enter radial axis title":"Kliknite pre zadanie n\xe1zvu radi\xe1lnej osi","Compare data on hover":"Porovna\u0165 hodnoty pri prejden\xed my\u0161ou","Double-click on legend to isolate one trace":"Dvojklikom na legendu izolujete jednu d\xe1tov\xfa sadu","Double-click to zoom back out":"Dvojklikom vr\xe1tite zv\xe4\u010d\u0161enie","Download plot as a png":"Ulo\u017ei\u0165 ako PNG","Download plot":"Ulo\u017ei\u0165","Edit in Chart Studio":"Editova\u0165 v Chart Studio","IE only supports svg.  Changing format to svg.":"IE podporuje iba SVG form\xe1t. Zmenen\xe9 na SVG.","Lasso Select":"V\xfdber lasom","Orbital rotation":"Rot\xe1cia (orbit\xe1lna)",Pan:"Pos\xfavanie","Produced with Plotly.js":"Vytvoren\xe9 pomocou Plotly.js",Reset:"Obnovi\u0165 nastavenie","Reset axes":"Obnovi\u0165 nastavenie os\xed","Reset camera to default":"Obnovi\u0165 nastavenie kamery do predvolen\xe9ho stavu","Reset camera to last save":"Obnovi\u0165 nastavenie kamery do posledn\xe9ho ulo\u017een\xe9ho stavu","Reset view":"Obnovi\u0165 nastavenie poh\u013eadu","Reset views":"Obnovi\u0165 nastavenie poh\u013eadov","Show closest data on hover":"Zobrazi\u0165 najbli\u017e\u0161iu hodnotu p\u0159i prejden\xed my\u0161ou","Snapshot succeeded":"Obr\xe1zok vytvoren\xfd","Sorry, there was a problem downloading your snapshot!":"Ospravedl\u0148ujeme sa, do\u0161lo k chybe pri s\u0165ahovan\xed obr\xe1zka!","Taking snapshot - this may take a few seconds":"Sn\xedmanie - m\xf4\u017ee trva\u0165 nieko\u013eko sek\xfand",Zoom:"Zv\xe4\u010d\u0161enie","Zoom in":"Zv\xe4\u010d\u0161i\u0165","Zoom out":"Zmen\u0161i\u0165","close:":"zavrie\u0165:",trace:"d\xe1tov\xe1 sada","lat:":"Lat.:","lon:":"Lon.:","q1:":"q1:","q3:":"q3:","source:":"zdroj:","target:":"cie\u013e:","lower fence:":"spodn\xe1 hranica:","upper fence:":"vrchn\xe1 hranica:","max:":"max.:","mean \xb1 \u03c3:":"priemer \xb1 \u03c3:","mean:":"priemer:","median:":"medi\xe1n:","min:":"min.:","new text":"nov\xfd text","Turntable rotation":"Rot\xe1cia (oto\u010dn\xfd stol\xedk)","Toggle Spike Lines":"Prepn\xfa\u0165 zobrazenie vodiacich \u010diar","open:":"otvori\u0165:","high:":"horn\xe1:","low:":"doln\xe1:","Toggle show closest data on hover":"Prepn\xfa\u0165 zobrazovanie najbli\u017e\u0161ej hodnoty pri prejden\xed my\u0161ou","incoming flow count:":"po\u010det \xfadajov na vstupe:","outgoing flow count:":"po\u010det \xfadajov na v\xfdstupe:","kde:":"kde:"},format:{days:["Nede\u013ea","Pondelok","Utorok","Streda","\u0160tvrtok","Piatok","Sobota"],shortDays:["Ned","Pon","Uto","Str","\u0160tv","Pia","Sob"],months:["Janu\xe1r","Febru\xe1r","Marec","Apr\xedl","M\xe1j","J\xfan","J\xfal","August","September","Okt\xf3ber","November","December"],shortMonths:["Jan","Feb","Mar","Apr","M\xe1j","J\xfan","J\xfal","Aug","Sep","Okt","Nov","Dec"],date:"%d.%m.%Y",decimal:",",thousands:" "}};"undefined"==typeof Plotly?(window.PlotlyLocales=window.PlotlyLocales||[],window.PlotlyLocales.push(locale)):Plotly.register(locale);