{"type": "Topology", "objects": {"coastlines": {"type": "GeometryCollection", "geometries": [{"type": "LineString", "arcs": [0, 1]}, {"type": "LineString", "arcs": [2, 3, 4, 5]}, {"type": "LineString", "arcs": [6, 7, 8, 9, 10]}, {"type": "LineString", "arcs": [11]}, {"type": "LineString", "arcs": [12]}, {"type": "LineString", "arcs": [13]}, {"type": "LineString", "arcs": [14]}, {"type": "LineString", "arcs": [15]}, {"type": "LineString", "arcs": [16]}, {"type": "LineString", "arcs": [17]}, {"type": "LineString", "arcs": [18]}, {"type": "LineString", "arcs": [19]}, {"type": "LineString", "arcs": [20]}, {"type": "LineString", "arcs": [21]}, {"type": "LineString", "arcs": [22]}, {"type": "LineString", "arcs": [23]}, {"type": "LineString", "arcs": [24]}, {"type": "LineString", "arcs": [25]}, {"type": "LineString", "arcs": [26]}, {"type": "LineString", "arcs": [27]}, {"type": "LineString", "arcs": [28]}, {"type": "LineString", "arcs": [29]}, {"type": "LineString", "arcs": [30]}, {"type": "LineString", "arcs": [31]}, {"type": "LineString", "arcs": [32]}, {"type": "LineString", "arcs": [33]}, {"type": "LineString", "arcs": [34]}, {"type": "LineString", "arcs": [35]}, {"type": "LineString", "arcs": [36]}, {"type": "LineString", "arcs": [37]}, {"type": "LineString", "arcs": [38]}, {"type": "LineString", "arcs": [39]}, {"type": "LineString", "arcs": [40]}, {"type": "LineString", "arcs": [41]}, {"type": "LineString", "arcs": [42]}, {"type": "LineString", "arcs": [43]}, {"type": "LineString", "arcs": [44]}, {"type": "LineString", "arcs": [45]}, {"type": "LineString", "arcs": [46]}, {"type": "LineString", "arcs": [47]}, {"type": "LineString", "arcs": [48]}, {"type": "LineString", "arcs": [49]}, {"type": "LineString", "arcs": [50]}, {"type": "LineString", "arcs": [51]}, {"type": "LineString", "arcs": [52]}, {"type": "LineString", "arcs": [53]}, {"type": "LineString", "arcs": [54]}, {"type": "MultiLineString", "arcs": [[55], [56]]}, {"type": "LineString", "arcs": [57]}, {"type": "LineString", "arcs": [58]}, {"type": "LineString", "arcs": [59]}, {"type": "MultiLineString", "arcs": [[60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, -59, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97], [98], [99, 100, 101], [102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117]]}, {"type": "LineString", "arcs": [118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336]}, {"type": "LineString", "arcs": [337, 338, 339]}, {"type": "LineString", "arcs": [340, 341]}, {"type": "LineString", "arcs": [342, 343]}, {"type": "LineString", "arcs": [344, 345, 346]}, {"type": "LineString", "arcs": [347]}, {"type": "LineString", "arcs": [348]}, {"type": "LineString", "arcs": [349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370]}, {"type": "LineString", "arcs": [371]}, {"type": "LineString", "arcs": [372]}, {"type": "LineString", "arcs": [373]}, {"type": "LineString", "arcs": [374]}, {"type": "LineString", "arcs": [375]}]}, "land": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "arcs": [[34]]}, {"type": "Polygon", "arcs": [[339, 337, 338]]}, {"type": "Polygon", "arcs": [[36]]}, {"type": "Polygon", "arcs": [[35]]}, {"type": "Polygon", "arcs": [[37]]}, {"type": "Polygon", "arcs": [[15]]}, {"type": "Polygon", "arcs": [[31]]}, {"type": "Polygon", "arcs": [[30]]}, {"type": "Polygon", "arcs": [[1, 387]]}, {"type": "Polygon", "arcs": [[33]]}, {"type": "Polygon", "arcs": [[32]]}, {"type": "Polygon", "arcs": [[38]]}, {"type": "Polygon", "arcs": [[5, 2, 3, 4]]}, {"type": "Polygon", "arcs": [[41]]}, {"type": "Polygon", "arcs": [[29]]}, {"type": "Polygon", "arcs": [[40]]}, {"type": "Polygon", "arcs": [[47]]}, {"type": "Polygon", "arcs": [[49]]}, {"type": "Polygon", "arcs": [[50]]}, {"type": "Polygon", "arcs": [[39]]}, {"type": "Polygon", "arcs": [[48]]}, {"type": "Polygon", "arcs": [[42]]}, {"type": "Polygon", "arcs": [[43]]}, {"type": "Polygon", "arcs": [[59]]}, {"type": "Polygon", "arcs": [[388, 389, 8, 390, 391, 10, 6]]}, {"type": "Polygon", "arcs": [[44]]}, {"type": "Polygon", "arcs": [[45]]}, {"type": "MultiPolygon", "arcs": [[[122, 392, 124, 393, 394, 127, 395, 396, 397, 398, 399, 133, 400, 401, 136, 402, 403, 404, 405, 406, 142, 407, 408, 145, 409, 410, 411, 412, 413, 151, 414, 153, 415, 155, 416, 157, 417, 418, 419, 420, 162, 421, 422, 165, 423, 167, 424, 169, 425, 426, 172, 427, 174, 428, 429, 430, 431, 179, 180, 432, 182, 433, 434, 185, 435, 436, 188, 437, 190, 438, 192, 193, 439, 195, 440, 441, 198, 442, 200, 443, 444, 203, 204, 445, 446, 207, 447, 448, 449, 211, 450, 213, 214, 451, 452, 453, 218, 454, 220, 455, 456, 223, 457, 225, 458, 459, 228, 229, 460, 461, 232, 233, 462, 235, 463, 464, 238, 465, 240, 466, 242, 467, 468, 245, 469, 470, 471, 249, 250, 472, 473, 253, 474, 475, 476, 257, 258, 477, 478, 261, 479, 263, 480, 481, 266, 482, 483, 269, 484, 271, 485, 486, 487, 275, 276, 488, 278, 279, 280, 489, 282, 490, 491, 285, 492, 287, 493, 494, 495, 291, 496, 293, 497, 295, 296, 498, 499, 299, 500, 501, 302, 502, 503, 504, 306, 505, 308, 506, 310, 311, 507, 508, 314, 509, 510, 317, 511, 319, 512, 321, 513, 323, 514, 515, 326, 516, 328, 329, 517, 518, 62, 519, 64, 520, 521, 67, 68, 69, 522, 71, 523, 524, 74, 525, 526, 527, 78, 528, 80, 529, 82, 530, 531, 85, 532, 87, 533, 534, -370, 535, 536, -367, 537, -365, -364, 538, -362, 539, 540, -359, 541, -357, 542, -355, 543, -353, 544, -351, 545, 546, 547, 548, 549]], [[90, 550, 92, 551, 94, 552, 553]]]}]}, "ocean": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "arcs": [[369, 370, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 554, 365, 366, 367, 368]]}, {"type": "MultiPolygon", "arcs": [[[-372], [-374], [-373], [-55], [-376], [-54], [-52], [-53], [-375], [-344, 555], [-117, 556, -115, 557, -113, 558, -111, 559, -109, 560, -107, 561, -105, 562, -103, 563, -56, -57, 564]], [[-102, 565, -100, 566]], [[-99, 567, -98, 568, -96, -95, -94, -93, -92, -91, -90, -89, 58, -88, -87, -86, -85, -84, -83, -82, -81, -80, -79, -78, -77, -76, -75, -74, -73, -72, -71, -70, -69, -68, -67, -66, -65, -64, -63, -62, -61, 569, -58, 570], [-12], [-11, -392, -391, -9, -390, -389, -7]], [[-29], [-15], [-14], [-349], [-17], [-26], [-27], [-28], [-13], [-20], [-1, -2], [-18], [-19], [-25], [-24], [-23], [-21], [-22], [-338, -340, -339], [-35], [-37], [-36], [-38], [-16], [-39], [-4, -3, -6, -5], [-34], [-33], [-31], [-32], [-42], [-48], [-50], [-51], [-41], [-40], [-49], [-43], [-348], [-44], [-45], [-60], [-46], [-47], [-30], [-346, 571], [-341, 572, -337, 573, -335, 574, -333, 575, -331, -330, -329, -328, -327, -326, -325, -324, -323, -322, -321, -320, -319, -318, -317, -316, -315, -314, -313, -312, -311, -310, -309, -308, -307, -306, -305, -304, -303, -302, -301, -300, -299, -298, -297, -296, -295, -294, -293, -292, -291, -290, -289, -288, -287, -286, -285, -284, -283, -282, -281, -280, -279, -278, -277, -276, -275, -274, -273, -272, -271, -270, -269, -268, -267, -266, -265, -264, -263, -262, -261, -260, -259, -258, -257, -256, -255, -254, -253, -252, -251, -250, -249, -248, -247, -246, -245, -244, -243, -242, -241, -240, -239, -238, -237, -236, -235, -234, -233, -232, -231, -230, -229, -228, -227, -226, -225, -224, -223, -222, -221, -220, -219, -218, -217, -216, -215, -214, -213, -212, -211, -210, -209, -208, -207, -206, -205, -204, -203, -202, -201, -200, -199, -198, -197, -196, -195, -194, -193, -439, -191, -190, -189, -188, -187, -186, -185, -184, -183, -182, -181, -180, -432, -178, -177, -176, -175, -174, -173, -172, -171, -170, -169, -168, -167, -166, -165, -164, -163, -162, -161, -160, -159, -158, -157, -156, -155, -154, -153, -152, -151, -150, -412, 576, -147, -146, -145, -144, -143, -142, -141, -140, -139, -138, -137, -136, -135, -134, -133, -132, -131, -130, -129, -128, -127, -126, -125, -124, -123, -122, 577, -120, 578]]]}]}, "lakes": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "arcs": [[579]]}, {"type": "Polygon", "arcs": [[580, 581]]}]}, "rivers": {"type": "GeometryCollection", "geometries": [{"type": "LineString", "arcs": [376]}, {"type": "LineString", "arcs": [377, 378, 379, 380, 381, 382, 383]}, {"type": "LineString", "arcs": [384]}, {"type": "LineString", "arcs": [385]}, {"type": "LineString", "arcs": [386]}]}, "countries": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "properties": {"ct": [67.28, 48.19]}, "id": "KAZ", "arcs": [[582, 583, 584, 585, 586, 587, -358, -357, -356, -355, -354, -353, -352, -351, 545]]}, {"type": "Polygon", "properties": {"ct": [63.2, 41.75]}, "id": "UZB", "arcs": [[-587, -586, -585, 588, 589, 590, 591]]}, {"type": "MultiPolygon", "properties": {"ct": [114.02, -0.25]}, "id": "IDN", "arcs": [[[387, 1]], [[592, 338]], [[15]], [[4, 593]], [[30]], [[31]], [[32]], [[33]], [[34]], [[35]], [[36]], [[37]], [[38]]]}, {"type": "Polygon", "properties": {"ct": [125.97, -8.77]}, "id": "TLS", "arcs": [[339, 337, -593]]}, {"type": "Polygon", "properties": {"ct": [35, 31.48]}, "id": "ISR", "arcs": [[594, 595, 596, 517, 61, 62, 63, 64, 65, 66, 597, 598]]}, {"type": "Polygon", "properties": {"ct": [35.87, 33.91]}, "id": "LBN", "arcs": [[-598, 67, 599]]}, {"type": "Polygon", "properties": {"ct": [35.27, 31.94]}, "id": "PSE", "arcs": [[-596, 600]]}, {"type": "Polygon", "properties": {"ct": [36.78, 31.25]}, "id": "JOR", "arcs": [[-595, 601, 602, 603, 329, -597, -601]]}, {"type": "Polygon", "properties": {"ct": [54.21, 23.87]}, "id": "ARE", "arcs": [[270, 271, 272, 273, 274, 275, 604, 279, 605, 606]]}, {"type": "Polygon", "properties": {"ct": [51.18, 25.32]}, "id": "QAT", "arcs": [[264, 265, 266, 267, 268, 607]]}, {"type": "Polygon", "properties": {"ct": [47.6, 29.31]}, "id": "KWT", "arcs": [[258, 259, 260, 608, 609]]}, {"type": "Polygon", "properties": {"ct": [43.76, 33.04]}, "id": "IRQ", "arcs": [[-603, 610, 611, 612, 257, -610, 613]]}, {"type": "MultiPolygon", "properties": {"ct": [56.1, 20.58]}, "id": "OMN", "arcs": [[[-606, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 614, 615]], [[-605, 276, 277, 278]]]}, {"type": "Polygon", "properties": {"ct": [104.88, 12.68]}, "id": "KHM", "arcs": [[616, 617, 618, 191, 192]]}, {"type": "Polygon", "properties": {"ct": [101.01, 15.02]}, "id": "THA", "arcs": [[-617, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 619, 214, 215, 620, 380, 621, 382, 622]]}, {"type": "Polygon", "properties": {"ct": [103.75, 18.44]}, "id": "LAO", "arcs": [[-618, -623, -383, -622, -381, -380, 623, 624]]}, {"type": "Polygon", "properties": {"ct": [96.51, 21.02]}, "id": "MMR", "arcs": [[-621, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 625, 626, 627, 378, 379]]}, {"type": "Polygon", "properties": {"ct": [106.29, 16.66]}, "id": "VNM", "arcs": [[-619, -625, 628, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190]]}, {"type": "MultiPolygon", "properties": {"ct": [127.17, 40.14]}, "id": "PRK", "arcs": [[[629, 629, 629]], [[549, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 630, 139, 140, 141, 142, 143, 144, 145, 146, 147, 631]]]}, {"type": "Polygon", "properties": {"ct": [127.82, 36.43]}, "id": "KOR", "arcs": [[-631, 132, 133, 134, 135, 136, 137, 138]]}, {"type": "Polygon", "properties": {"ct": [102.95, 46.82]}, "id": "MNG", "arcs": [[547, 632]]}, {"type": "Polygon", "properties": {"ct": [79.59, 22.93]}, "id": "IND", "arcs": [[-627, 633, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 634, 635, 636, 637, 638, 639]]}, {"type": "Polygon", "properties": {"ct": [90.27, 23.84]}, "id": "BGD", "arcs": [[-626, 227, 228, 229, 230, 231, 232, -634]]}, {"type": "Polygon", "properties": {"ct": [90.47, 27.43]}, "id": "BTN", "arcs": [[-639, 640]]}, {"type": "Polygon", "properties": {"ct": [84.01, 28.24]}, "id": "NPL", "arcs": [[-637, 641]]}, {"type": "Polygon", "properties": {"ct": [69.41, 29.97]}, "id": "PAK", "arcs": [[-635, 250, 642, 643, 644]]}, {"type": "Polygon", "properties": {"ct": [66.09, 33.86]}, "id": "AFG", "arcs": [[-591, 645, 646, -644, 647, 648]]}, {"type": "Polygon", "properties": {"ct": [71.03, 38.58]}, "id": "TJK", "arcs": [[-590, 649, 650, -646]]}, {"type": "Polygon", "properties": {"ct": [74.62, 41.51]}, "id": "KGZ", "arcs": [[-584, 651, -650, -589]]}, {"type": "Polygon", "properties": {"ct": [59.28, 39.09]}, "id": "TKM", "arcs": [[-588, -592, -649, 652, -363, -362, -361, -360, -359]]}, {"type": "Polygon", "properties": {"ct": [54.29, 32.52]}, "id": "IRN", "arcs": [[-613, 653, 654, 655, 656, -365, -364, -653, -648, -643, 251, 252, 253, 254, 255, 256]]}, {"type": "Polygon", "properties": {"ct": [38.54, 35.01]}, "id": "SYR", "arcs": [[-599, -600, 68, 657, -611, -602]]}, {"type": "Polygon", "properties": {"ct": [45, 40.22]}, "id": "ARM", "arcs": [[-656, 658, 659, 660, 661]]}, {"type": "MultiPolygon", "properties": {"ct": [35.39, 38.99]}, "id": "TUR", "arcs": [[[-612, -658, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 662, -660, -654]], [[89, 90, 91, 92, 93, 94, 552]]]}, {"type": "Polygon", "properties": {"ct": [80.67, 7.7]}, "id": "LKA", "arcs": [[29]]}, {"type": "MultiPolygon", "properties": {"ct": [103.87, 36.61]}, "id": "CHN", "arcs": [[[42]], [[-583, 546, -633, 548, -632, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 431, 179, -629, -624, -379, -628, -640, -641, -638, -642, -636, -645, -647, -651, -652]]]}, {"type": "Polygon", "properties": {"ct": [120.97, 23.74]}, "id": "TWN", "arcs": [[43]]}, {"type": "MultiPolygon", "properties": {"ct": [47.68, 40.28]}, "id": "AZE", "arcs": [[[534, -370, -369, -368, -367, -366, -657, -662, 663]], [[-655, -659]]]}, {"type": "Polygon", "properties": {"ct": [43.48, 42.16]}, "id": "GEO", "arcs": [[533, -664, -661, -663, 84, 85, 86, 87]]}, {"type": "MultiPolygon", "properties": {"ct": [121.54, 15.75]}, "id": "PHL", "arcs": [[[39]], [[40]], [[41]], [[47]], [[48]], [[49]], [[50]]]}, {"type": "MultiPolygon", "properties": {"ct": [114.68, 3.55]}, "id": "MYS", "arcs": [[[-620, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213]], [[-594, 5, 664, 3]]]}, {"type": "Polygon", "properties": {"ct": [114.92, 4.69]}, "id": "BRN", "arcs": [[-665, 2]]}, {"type": "MultiPolygon", "properties": {"ct": [136.88, 36.02]}, "id": "JPN", "arcs": [[[44]], [[45]], [[59]]]}, {"type": "Polygon", "properties": {"ct": [47.54, 15.91]}, "id": "YEM", "arcs": [[-615, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 665]]}, {"type": "Polygon", "properties": {"ct": [44.52, 24.12]}, "id": "SAU", "arcs": [[-604, -614, -609, 261, 262, 263, -608, 269, -607, -616, -666, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328]]}, {"type": "Polygon", "arcs": [[391, 10, 6, 388, 666]]}, {"type": "Polygon", "properties": {"ct": [33.04, 34.91]}, "id": "CYP", "arcs": [[-667, 389, 8, 390]]}]}, "subunits": {"type": "GeometryCollection", "geometries": []}}, "arcs": [[[7636, 4838], [105, -41], [112, -33], [42, -31], [33, -29], [10, -35], [100, -36], [15, -32], [-56, -6], [14, -39], [54, -39], [39, -62], [35, 2], [-2, -27], [46, -10], [-18, -11], [65, -24], [-7, -17], [-40, -5], [-15, 16], [-52, 6], [-61, 9], [-48, 38], [-34, 32], [-31, 52], [-80, 26], [-51, -17], [-37, -20], [8, -43], [-48, -20], [-34, 9], [-62, 3]], [[7638, 4454], [-54, 48], [-62, 12], [-15, -17], [-77, -2], [26, 48], [38, 17], [-15, 64], [-30, 49], [-117, 50], [-50, 5], [-91, 55], [-18, -29], [-23, -5], [-14, 21], [0, 26], [-46, 29], [65, 21], [43, -1], [-5, 16], [-89, 0], [-24, 35], [-54, 11], [-26, 29], [82, 14], [31, 19], [98, -24], [9, -22], [17, -95], [63, -35], [50, 62], [70, 36], [54, 0], [52, -21], [45, -21], [65, -11]], [[6012, 5258], [24, 22], [51, 32]], [[6087, 5312], [47, 41], [31, 46], [24, 0], [31, -30], [3, -25], [40, -17], [50, -17], [-4, -23], [-41, -3], [11, -29], [-44, -20]], [[6235, 5235], [-35, -53], [45, -56], [-11, -27], [68, -55], [-72, -7], [-20, -40], [3, -53], [-58, -41], [-2, -58], [-23, -90], [-9, 21], [-69, -27], [-24, 36], [-43, 3], [-31, 19], [-72, -21], [-22, 29], [-39, -4], [-50, 7], [-10, 79], [-30, 17], [-29, 50], [-8, 52], [7, 54], [36, 39]], [[5737, 5109], [44, -20], [47, 11], [12, 50], [26, 11], [73, 13], [43, 46], [30, 38]], [[1088, 7076], [43, -1], [55, 18], [-41, -25]], [[1145, 7068], [7, -16]], [[1152, 7052], [-62, -24], [-30, 8], [-14, 23]], [[1046, 7059], [33, 3]], [[1079, 7062], [9, 14]], [[684, 7071], [-7, -18], [-88, -5], [1, 10], [-74, 12], [11, 25], [33, -20], [48, 3], [45, -4], [-2, -10], [33, 7]], [[2093, 4256], [16, -25], [15, -39], [10, -70], [16, -28], [-6, -28], [-11, -18], [-20, 35], [-12, -18], [12, -43], [-6, -25], [-16, -14], [-4, -50], [-24, -68], [-30, -82], [-37, -111], [-24, -82], [-27, -68], [-49, -14], [-53, -25], [-35, 15], [-48, 21], [-17, 31], [-4, 52], [-21, 47], [-6, 43], [11, 42], [28, 10], [0, 20], [29, 44], [6, 38], [-15, 28], [-11, 37], [-5, 54], [21, 33], [8, 38], [31, 2], [33, 12], [23, 11], [27, 0], [34, 34], [50, 36], [18, 30], [-8, 25], [26, -7], [33, 41], [1, 35], [20, 27], [21, -26]], [[9224, 4055], [38, -34], [-20, -8], [-20, 26], [2, 16]], [[9199, 4068], [-9, 16], [-1, 45], [29, -18], [10, -47], [-17, 7], [-12, -3]], [[7224, 4585], [-6, 44], [11, 21], [13, 20], [13, -17], [0, -28], [-31, -40]], [[9137, 3749], [50, -36], [31, -28], [-23, -14], [-33, 16], [-43, 27], [-39, 31], [-40, 42], [-9, 19], [26, 0], [34, -20], [27, -20], [19, -17]], [[8341, 4775], [23, -18], [7, -31], [-19, -16], [-11, 35], [-14, 23], [-28, 19], [-34, 25], [-44, 18], [17, 14], [33, -17], [20, -13], [26, -14], [24, -25]], [[8260, 4647], [-33, -14], [-31, -14], [-33, 0], [-49, 17], [-35, 16], [5, 19], [54, -9], [34, 5], [9, 28], [8, 1], [6, -31], [35, 5], [17, 20], [34, 21], [-7, 35], [36, 1], [13, -10], [-1, -33], [-21, -36], [-32, -5], [-9, -16]], [[8915, 4373], [17, -20], [-42, 1], [-23, 36], [36, -14], [12, -3]], [[8889, 4425], [-9, -10], [-45, 51], [-13, 35], [21, 0], [22, -47], [24, -29]], [[8839, 4409], [-24, -1], [-37, 6], [-13, 9], [4, 23], [40, -9], [20, -12], [10, -16]], [[8765, 4519], [14, -19], [3, -12], [-47, 25], [-34, 21], [-22, 20], [9, 6], [27, -14], [50, -27]], [[8614, 4577], [24, -19], [-12, -3], [-27, 13], [-25, 24], [3, 10], [37, -25]], [[8469, 4676], [19, -13], [29, -37], [29, -20], [-9, -17], [-17, -6], [-26, 23], [-26, 37], [-13, 45], [8, 6], [6, -18]], [[9810, 2630], [-23, -31], [-30, -41], [-46, -23], [-11, 15], [-25, 9], [35, 48], [-20, 33], [-65, 23], [1, 22], [44, 20], [10, 46], [-2, 38], [-25, 39], [2, 11], [-29, 24], [-48, 52], [-25, 42], [22, 4], [33, -32], [47, -16], [17, -52], [44, -62], [2, 40], [27, -16], [9, -45], [49, -19], [41, -5], [34, 23], [31, -7], [-15, -52], [-18, -35], [-46, 2], [-16, -18], [5, -26], [-9, -11]], [[9373, 2425], [52, 31], [36, 30], [27, 44], [23, 15], [9, 33], [43, 27], [13, -25], [14, -24], [43, 24], [17, -25], [0, -25], [-22, -27], [-40, -44], [-31, -23], [23, -29], [-47, 0], [-52, -23], [-16, -38], [-35, -60], [-47, -26], [-30, -17], [-56, 1], [-40, 20], [-65, 4], [-11, 22], [33, 43], [76, 58], [39, 11], [44, 23]], [[8041, 2587], [36, -4], [5, -70], [-21, -20], [-6, -48], [-22, 16], [-42, -41], [-12, 3], [-37, 2], [-38, 51], [-8, 39], [-35, 51], [1, 27], [40, -5], [59, -21], [33, 8], [47, 12]], [[6736, 3093], [-65, -30], [-52, -14], [-12, -31], [-22, -24], [-52, -1], [-38, -5], [-53, 10], [-44, -6], [-41, -3], [-36, -31], [-18, 3], [-31, -17], [-29, -19], [-44, 2], [-41, 0], [-64, 38], [-32, 11], [1, 34], [30, 8], [10, 14], [-2, 21], [8, 41], [-7, 35], [-32, 59], [-10, 34], [2, 33], [-24, 39], [-1, 17], [-27, 23], [-8, 47], [-34, 46], [-9, 25], [27, -25], [-20, 55], [30, -18], [18, -22], [-2, 30], [-29, 46], [-6, 19], [-14, 17], [6, 34], [13, 15], [8, 29], [-7, 35], [25, 42], [5, -45], [26, 41], [49, 20], [29, 25], [47, 21], [27, 5], [17, -7], [48, 22], [36, 6], [10, 13], [16, 5], [33, -1], [64, 17], [33, 27], [15, 31], [36, 30], [3, 23], [1, 32], [43, 51], [25, -51], [26, 11], [-22, 28], [19, 29], [27, -13], [7, 45], [34, 29], [14, 23], [31, 10], [1, 17], [26, -7], [1, 15], [27, 8], [29, 8], [45, -27], [34, -35], [38, 0], [38, -6], [-13, 32], [29, 48], [28, 15], [-10, 15], [26, 33], [37, 21], [31, -7], [51, 11], [-1, 30], [-44, 20], [32, 8], [40, -14], [32, -24], [51, -15], [18, 6], [37, -19], [35, 17], [23, -5], [14, 11], [28, -29], [-16, -31], [-23, -24], [-21, -2], [7, -24], [-17, -29], [-22, -29], [4, -17], [48, -32], [47, -19], [31, -20], [44, -35], [17, 0], [32, -15], [9, -18], [58, -20], [40, 20], [12, 31], [12, 26], [8, 33], [18, 47], [-8, 28], [4, 17], [-7, 34], [8, 44], [12, 12], [-10, 20], [15, 31], [11, 33], [2, 16], [22, 23], [18, -29], [4, -37], [15, -7], [2, -25], [22, -30], [5, -33], [-2, -22], [22, -46], [39, 22], [20, -25], [29, -23], [-7, -26], [13, -50], [10, -30], [15, -7], [16, -50], [-6, -31], [20, -40], [66, -31], [43, -28], [40, -25], [-8, -15], [35, -37], [24, -63], [24, 13], [24, -26], [15, 9], [11, -62], [43, -36], [28, -23], [47, -48], [17, -47], [2, -34], [-5, -36], [29, -50], [-3, -52], [-11, -28], [-16, -52], [1, -34], [-12, -42], [-27, -54], [-44, -29], [-23, -45], [-20, -29], [-18, -51], [-23, -30], [-15, -44], [-8, -40], [3, -19], [-35, -20], [-68, -3], [-56, -24], [-28, -23], [-36, -25], [-50, 26], [-37, 11], [9, 30], [-33, -11], [-53, -42], [-53, 15], [-34, 10], [-35, 4], [-58, 17], [-39, 36], [-12, 45], [-14, 30], [-30, 24], [-58, 7], [20, 28], [-15, 44], [-29, -41], [-54, -10], [32, 32], [9, 34], [23, 29], [-5, 44], [-49, -51], [-38, -20], [-23, -47], [-47, 25], [1, 31], [-37, 43], [-32, 22], [11, 13], [-78, 36], [-42, 2], [-59, 28], [-108, -5], [-79, -21], [-69, -20], [-57, 4]], [[4047, 5434], [-9, -61], [-25, -17], [-53, -13], [-29, 47], [-10, 84], [27, 96], [42, -33], [28, -41], [29, -62]], [[6931, 4826], [67, -17], [22, -45], [-52, 24], [-50, 5], [-34, -4], [-42, 2], [14, 33], [75, 2]], [[6780, 4768], [-42, 11], [-12, 25], [61, 3], [15, -20], [-22, -19]], [[6844, 5119], [4, -32], [36, -5], [6, -24], [-4, -52], [-31, 6], [-9, -36], [25, -31], [-17, -7], [-25, 37], [-18, 76], [13, 47], [20, 21]], [[6540, 5043], [70, 2], [60, 43], [11, -13], [-49, -59], [-46, -11], [-58, 11], [-101, -2], [-53, -9], [-9, -45], [55, -52], [32, 27], [113, 20], [-5, -27], [-26, 8], [-26, -34], [-54, -23], [58, -76], [-11, -20], [54, -68], [0, -39], [-33, -17], [-23, 21], [29, 48], [-60, -23], [-15, 17], [8, 22], [-44, 35], [5, 57], [-40, -18], [5, -68], [2, -85], [-38, -8], [-26, 17], [17, 54], [-9, 57], [-26, 1], [-19, 40], [25, 39], [9, 46], [30, 89], [13, 24], [52, 44], [47, -17], [76, -8]], [[6381, 4387], [-81, 41], [57, 12], [32, -18], [21, -18], [-4, -16], [-25, -1]], [[6444, 4488], [41, 5], [54, 21], [-9, -33], [-91, -16], [-81, 7], [0, 22], [48, 12], [38, -18]], [[6258, 4498], [37, 5], [15, -25], [-70, -12], [-42, -8], [-33, 1], [21, 34], [34, 0], [16, 21], [22, -16]], [[5665, 4613], [9, -21], [116, -6], [13, 24], [112, -28], [23, -38], [90, -11], [75, -35], [-69, -23], [-67, 24], [-55, -1], [-63, 4], [-56, 10], [-71, 23], [-44, 6], [-25, -8], [-111, 25], [-10, 25], [-56, 4], [42, 57], [73, -4], [49, -23], [25, -4]], [[5416, 4927], [10, -41], [21, -33], [45, -5], [29, -37], [-15, -74], [-2, -91], [-68, -1], [-51, 49], [-77, 48], [-26, 36], [-46, 48], [-30, 44], [-46, 83], [-54, 49], [-17, 51], [-23, 45], [-54, 38], [-32, 50], [-46, 33], [-63, 65], [-5, 30], [39, -2], [94, -12], [53, -57], [47, -40], [33, -25], [58, -63], [61, -1], [51, -41], [35, -49], [47, -27], [-25, -48], [35, -20], [22, -2]], [[6414, 5740], [-31, 44], [52, -2], [21, -21], [-17, -51], [-25, 30]], [[6520, 5579], [15, 17], [7, 36], [33, 4], [-10, -40], [45, 57], [-6, -56], [-21, -20], [-19, -37], [-19, -17], [-38, 40], [13, 16]], [[6749, 5487], [7, -39], [3, -33], [-20, -54], [-23, 60], [-28, -30], [19, -44], [-17, -27], [-71, 34], [-17, 43], [18, 28], [-38, 28], [-19, -25], [-29, 2], [-45, -32], [-10, 17], [24, 49], [38, 17], [33, 22], [22, -26], [46, 16], [10, 26], [43, 2], [-4, 45], [49, -28], [5, -30], [4, -21]], [[5725, 6063], [-49, 18], [-2, 51], [30, 27], [66, 16], [35, -1], [13, -23], [-26, -25], [-14, -34], [-53, -29]], [[6471, 6428], [-37, -94], [-26, -49], [-32, 50], [-7, 44], [36, 58], [49, 44], [27, -17], [-10, -36]], [[7689, 7299], [-56, -59], [1, -61], [-23, -47], [11, -29], [-32, -42], [-77, -27], [-107, -4], [-86, -67], [-41, 22], [-2, 44], [-106, -13], [-71, -27], [-71, -1], [61, -44], [-40, -100], [-39, -25], [-30, 23], [15, 53], [-38, 17], [-25, 41], [57, 18], [32, 37], [61, 30], [45, 41], [120, 17], [65, -12], [63, 105], [41, -28], [89, 59], [34, 23], [38, 72], [-10, 66], [26, 37], [64, 11], [33, -82], [-2, -48]], [[7855, 7581], [42, 25], [14, -66], [-90, -16], [-53, -59], [-95, 41], [-33, -65], [-68, -1], [-8, 59], [30, 45], [65, 3], [17, 82], [18, 46], [71, -62], [47, -19], [43, -13]], [[7752, 8155], [21, -57], [-2, -58], [25, -59], [61, -104], [-90, 19], [-37, -85], [59, -60], [-2, -42], [-45, 36], [-40, -46], [-11, 50], [6, 57], [-7, 64], [14, 44], [3, 79], [-35, 58], [5, 80], [56, 28], [-24, 27], [27, 8], [16, -39]], [[6272, 5540], [-80, -56], [29, 41], [44, 37], [37, 40], [31, 59], [11, -48], [-40, -33], [-32, -40]], [[6505, 6065], [-10, -25], [20, -42], [-16, -49], [-35, -19], [-10, -48], [14, -47], [32, -6], [27, 7], [75, -33], [-5, -32], [19, -14], [-6, -27], [-47, 29], [-23, 31], [-15, -22], [-39, 35], [-55, -9], [-30, 14], [3, 24], [19, 15], [-18, 13], [-8, -21], [-30, 34], [-9, 26], [-2, 56], [24, -19], [7, 92], [19, 53], [37, 0], [37, -17], [19, 16], [6, -15]], [[6487, 5664], [-10, 28], [37, -19], [38, 1], [-1, -25], [-28, -25], [-39, -18], [-2, 28], [5, 30]], [[6696, 5708], [17, -66], [-46, 15], [1, -19], [15, -37], [-29, -13], [-3, 42], [-18, 3], [-9, 35], [35, -4], [0, 22], [-37, 45], [58, -1], [16, -22]], [[1809, 9739], [118, 11], [92, 1], [12, -16], [35, 14], [57, 10], [90, -13], [-23, -9], [-82, -8], [-54, -5], [-8, -9], [-71, -10], [-66, 14], [35, 18], [-135, 2]], [[2334, 9336], [145, 52], [-17, 27], [136, 31], [200, 38], [202, 11], [103, 21], [118, 8], [42, -23], [-40, -18], [-215, -30], [-185, -28], [-188, -56], [-91, -57], [-95, -57], [13, -49], [116, -48], [-36, -5], [-198, 7], [-16, 26], [-110, 16], [-9, 32], [62, 13], [-2, 32], [120, 50], [-55, 7]], [[752, 9708], [-90, -32], [-176, -7], [-178, 10], [-11, 16], [-87, 1], [-66, 27], [187, 17], [88, -15], [61, 18], [153, -15], [119, -20]], [[589, 9578], [-135, -24], [-107, 14], [42, 15], [-37, 19], [126, 11], [24, -22], [87, -13]], [[9, 9685], [23, 20], [89, 2], [76, -20], [200, -44], [-153, -23], [-34, -44], [-53, -11], [-29, -49], [-73, -2], [-55, 15]], [[0, 9685], [9, 0]], [[0, 7240], [32, 4], [-22, -47], [9, -18], [-13, -30], [-6, 2]], [[1366, 7622], [69, -22], [77, -50]], [[7113, 6963], [33, 35], [35, -7], [25, 25], [44, -13], [8, -20], [-34, -36], [-25, 19], [-31, -14], [-16, -34], [-40, 17], [1, 28]], [[0, 6897], [15, -5], [28, -52], [55, -12], [85, -24], [65, -30], [29, 16], [29, 27], [-14, 45], [19, 28], [44, 28], [42, 8], [81, -12], [21, -26], [23, -1], [19, -10], [60, -7], [15, -19], [81, 1], [58, -16], [60, -17], [28, -9], [47, 18], [25, 17], [53, 5], [43, -7], [17, -29], [14, 19], [48, -14], [48, -3], [29, 14]], [[1167, 6830], [18, 20]], [[1185, 6850], [-4, 3]], [[1181, 6853], [16, 28]], [[1197, 6881], [12, 44]], [[1209, 6925], [9, 15]], [[1218, 6940], [2, 1]], [[1220, 6941], [21, 48], [30, 41], [2, 2]], [[1273, 7032], [-6, 45], [15, 25]], [[1282, 7102], [-23, 26], [23, 22], [-37, -5]], [[1245, 7145], [-50, 14]], [[1195, 7159], [-42, -34]], [[1153, 7125], [-92, -7]], [[1061, 7118], [-49, 32]], [[1012, 7150], [-65, 2]], [[947, 7152], [-14, -24]], [[933, 7128], [-42, -7]], [[891, 7121], [-59, 31]], [[832, 7152], [-66, -1], [-36, 58], [-44, 33]], [[686, 7242], [29, 46]], [[715, 7288], [-38, 28]], [[677, 7316], [67, 56]], [[744, 7372], [93, 3], [26, 45], [115, -8], [73, 38], [71, 17], [100, 1], [106, -42], [87, -22], [70, 9], [53, -6]], [[1538, 7407], [71, 31]], [[1609, 7438], [9, 25]], [[1618, 7463], [-15, 41]], [[1603, 7504], [-35, 21]], [[1568, 7525], [-34, 7], [-22, 18]], [[1366, 7622], [-52, 35], [44, 9], [50, 49], [-34, 24], [89, 24], [-1, 13], [-55, -10], [-48, -5], [-40, -19], [-57, -3], [-52, -22], [3, -36], [30, -15], [62, 4], [-12, -21], [-66, -10], [-83, -34], [-33, 12], [13, 27], [-66, 18], [10, 11], [58, 19], [-17, 14], [-94, 15], [-4, 22], [-57, -7], [-22, -33], [-47, -43], [1, -16], [-29, -12], [-18, 5], [-17, -71], [-32, -24], [-22, -42], [20, -34]], [[788, 7466], [7, -23]], [[795, 7443], [53, -19], [-11, -14]], [[837, 7410], [-72, -3]], [[765, 7407], [-26, -19], [-51, -31]], [[688, 7357], [-19, 27]], [[669, 7384], [1, 12]], [[670, 7396], [-37, 2], [-31, 6], [-74, -16], [42, -33]], [[570, 7355], [-31, -10], [-33, 0]], [[506, 7345], [-32, 31], [-12, -13], [14, -35], [30, -28], [-23, -13], [34, -27], [30, -17], [1, -33], [-56, 15], [18, -30], [-39, -6], [23, -52], [-40, -1], [-50, 26], [-23, 47], [-10, 39], [-24, 27], [-31, 34], [-4, 17], [-10, 4], [-1, 13], [-34, 20], [-5, 28], [5, 40], [8, 18], [-10, 9], [-13, 5], [-17, 19], [-26, 12], [-57, 22], [-35, 21], [-55, 17], [-51, 44], [12, 4], [-23, 21]], [[0, 7469], [9, -6], [47, 0], [15, -13], [-17, -11], [54, -22], [45, -18], [52, -30], [6, -11], [-11, -21], [-34, 27], [-53, 10], [-25, -38], [44, -22], [-8, -31], [-25, -3], [-32, -51], [-26, -4], [1, 18], [12, 31], [13, 13], [-23, 34], [-19, 30], [-25, 7]], [[0, 8179], [83, 24], [76, 20], [60, -10], [5, -15], [58, 0], [14, 26], [84, 19], [-13, 49], [2, 45], [30, 37], [57, 20], [48, -44], [49, 1], [11, 45], [7, 35], [-22, -8], [-38, 21], [-6, 34], [77, 17], [76, 8], [66, -10], [63, 2], [69, 33], [-64, 28], [-110, -5]], [[682, 8551], [-107, -22], [-98, -12]], [[477, 8517], [-35, 32], [-59, 20], [14, 58], [-30, 53], [29, 34], [55, 37], [139, 64], [40, 12], [-6, 25], [-84, 28], [-105, -17], [-58, -41], [9, -36], [-96, -47], [-117, -51], [-45, -83], [44, -41], [58, -33], [-56, -67], [-63, -13], [-23, -99], [-35, -55], [-53, 4]], [[0, 8993], [87, 38], [167, 73], [133, 26], [99, -3], [93, 49], [110, -3], [109, 12], [189, -43], [-78, -16], [67, -37], [62, 21], [100, -36], [166, -14], [229, -67], [46, -28], [4, -39], [-67, -31], [-99, -15], [-271, 44], [-44, -7], [99, -43]], [[1201, 8874], [4, -28], [4, -60]], [[1209, 8786], [78, -18], [47, -15], [8, 28]], [[1342, 8781], [-37, 26], [39, 22]], [[1344, 8829], [146, -37], [51, 15], [-40, 43], [141, 58], [56, -4], [56, -20], [36, 40], [-51, 35]], [[1739, 8959], [29, 36], [-44, 36]], [[1724, 9031], [170, -19], [34, -33], [-76, -7], [0, -33], [48, -20], [93, 13], [15, 37]], [[2008, 8969], [338, 79]], [[2346, 9048], [46, -3], [-60, -36]], [[2332, 9009], [77, -6], [42, 20]], [[2451, 9023], [113, 2], [90, 24], [69, -35], [69, 39], [-63, 34], [31, 19], [179, -17], [84, -19], [220, -67], [40, 31], [-61, 31], [-2, 12], [-73, 6], [20, 28], [-32, 46], [-2, 19], [112, 53], [39, 54], [45, 11], [161, -15], [12, -33], [-57, -48], [38, -19], [19, -41], [-14, -80], [67, -37], [-26, -39], [-118, -84], [69, -8], [24, 21], [67, 15], [16, 29], [52, 28], [-35, 34], [28, 39], [-66, 5], [-15, 32], [49, 59], [-79, 49], [108, 39], [-14, 42], [31, 1], [31, -32], [-23, -57], [64, -11], [-27, 43], [101, 23], [126, 3], [112, -34], [-54, 49], [-6, 63], [105, 12], [146, -3], [131, 8], [-49, 31], [70, 39], [70, 1], [118, 30], [160, 7], [20, 17], [159, 5], [50, -13], [136, 31], [111, -1], [17, 26], [58, 25], [143, 24], [104, -19], [-83, -15], [137, -9], [17, -29], [55, 14], [177, 0], [137, -29], [49, -22], [-16, -31], [-67, -17], [-159, -33], [-45, -17], [75, -9], [89, -15], [55, 12], [31, -38], [27, 15], [96, 9], [195, -9], [15, -28], [253, -9], [3, 45], [129, -10], [97, 0], [98, -31], [28, -37], [-36, -25], [76, -46], [95, -24], [59, 61], [97, -26], [103, 16], [117, -18], [45, 16], [99, -8], [-44, 55], [80, 25], [548, -38], [51, -35], [159, -45], [245, 11], [120, -10], [51, -24], [-8, -43], [75, -17], [81, 12], [108, 2], [114, -12]], [[9147, 9084], [116, 6], [104, -52]], [[9367, 9038], [76, 19]], [[9443, 9057], [-50, 38], [27, 26]], [[9420, 9121], [194, -17], [126, 4], [174, -28]], [[9914, 9080], [85, -26]], [[9999, 8819], [-78, -26]], [[9921, 8793], [-79, 5], [55, -32], [36, -48], [28, -16], [7, -25], [-16, -15], [-113, 13], [-169, -45], [-54, -7], [-93, -41], [-88, -36], [-22, -27], [-87, 41], [-158, -46], [-27, 22], [-58, -26], [-82, 8], [-19, -38], [-73, -57], [2, -24], [69, -13], [-8, -86]], [[8972, 8300], [-56, -3], [-26, -49]], [[8890, 8248], [25, -25], [-106, -30], [-21, -67], [-90, -15], [-18, -60], [-88, -55], [-22, 41], [-26, 86], [-34, 131], [29, 82], [52, 35], [3, 27], [94, 14], [108, 74], [104, 60], [109, 47], [49, 83], [-74, -5], [-36, -48], [-154, -65], [-49, 73], [-157, -20], [-151, -99], [50, -36], [-136, -16], [-93, -6], [4, 43], [-94, 9], [-75, -29], [-185, 10], [-200, -17], [-196, -115], [-232, -139], [95, -8], [30, -37], [59, -13], [39, 30], [66, -4], [88, -65], [2, -50], [-48, -59], [-5, -70], [-27, -95], [-92, -85], [-20, -41], [-82, -68], [-82, -68], [-39, -35], [-81, -35], [-38, 0], [-38, 28], [-81, -43], [-10, -19]], [[7016, 7479], [-23, 3]], [[6993, 7482], [-26, -20]], [[6967, 7462], [-18, -20], [2, -42]], [[6951, 7400], [-31, -13]], [[6920, 7387], [-11, -11]], [[6909, 7376], [-23, -17], [-40, -10], [-26, -16]], [[6820, 7333], [-2, -25]], [[6818, 7308], [-7, -7]], [[6811, 7301], [24, -9]], [[6835, 7292], [34, -26]], [[6869, 7266], [52, -70]], [[6921, 7196], [15, -38], [1, -68], [-23, -32], [-55, -11], [-48, -25], [-55, -5]], [[6756, 7017], [-7, 32]], [[6749, 7049], [11, 44]], [[6760, 7093], [-26, 62]], [[6734, 7155], [45, 10]], [[6779, 7165], [-42, 50]], [[6737, 7215], [-29, 11]], [[6708, 7226], [-8, -11]], [[6700, 7215], [-17, -5]], [[6683, 7210], [-2, 11], [-16, 6], [-16, 9], [16, 26], [14, 7]], [[6679, 7269], [-5, 11]], [[6674, 7280], [15, 32]], [[6689, 7312], [-4, 9]], [[6685, 7321], [-35, 7]], [[6650, 7328], [-29, 16]], [[6621, 7344], [-84, -18]], [[6537, 7326], [-45, -27]], [[6492, 7299], [-65, -16]], [[6427, 7283], [32, 27], [-13, 23], [48, 40], [-32, 30], [-52, -20]], [[6410, 7383], [-69, -41]], [[6341, 7342], [-37, -38]], [[6304, 7304], [-60, -3]], [[6244, 7301], [-31, -28], [32, -39]], [[6245, 7234], [50, -10]], [[6295, 7224], [2, -27], [48, -17], [68, 42]], [[6413, 7222], [54, -23]], [[6467, 7199], [39, -1]], [[6506, 7198], [10, -31]], [[6516, 7167], [-86, -17]], [[6430, 7150], [-28, -31], [-59, -30], [-31, -41], [65, -33], [24, -57], [36, -54], [42, -46], [-1, -44]], [[6478, 6814], [-38, -16]], [[6440, 6798], [14, -31]], [[6454, 6767], [36, -18]], [[6490, 6749], [-10, -48]], [[6480, 6701], [-15, -47], [-34, -5]], [[6431, 6649], [-44, -64]], [[6387, 6585], [-49, -77], [-56, -71]], [[6282, 6437], [-84, -54]], [[6198, 6383], [-84, -50]], [[6114, 6333], [-68, -6]], [[6046, 6327], [-37, -27]], [[6009, 6300], [-21, 20]], [[5988, 6320], [-35, -30]], [[5953, 6290], [-84, -29]], [[5869, 6261], [-64, -9]], [[5805, 6252], [-17, -49], [-4, -14]], [[5784, 6189], [-34, -3], [-16, 43], [15, 23], [-82, 18], [-28, -9]], [[5639, 6261], [-81, -51]], [[5558, 6210], [-51, -55]], [[5507, 6155], [-13, -41], [46, -62]], [[5540, 6052], [57, -77]], [[5597, 5975], [55, -37]], [[5652, 5938], [37, -47]], [[5689, 5891], [28, -109]], [[5717, 5782], [-8, -104]], [[5709, 5678], [-51, -38], [-69, -38], [-50, -49]], [[5539, 5553], [-75, -55]], [[5464, 5498], [-22, 38], [17, 39], [-45, 34]], [[5414, 5609], [-51, 8], [-25, 31]], [[5338, 5648], [-30, 61]], [[5308, 5709], [-55, 27]], [[5253, 5736], [-52, -1]], [[5201, 5735], [9, 46]], [[5210, 5781], [-53, 0]], [[5157, 5781], [-5, -65]], [[5152, 5716], [-33, -86], [-19, -52]], [[5100, 5578], [4, -43]], [[5104, 5535], [39, -1], [25, -54], [11, -51]], [[5179, 5429], [34, -34]], [[5213, 5395], [36, -7]], [[5249, 5388], [32, -30]], [[5281, 5358], [14, -6], [35, -35], [26, -40], [3, -40]], [[5359, 5237], [-6, -26]], [[5353, 5211], [6, -21]], [[5359, 5190], [4, -35], [22, -16], [23, -52]], [[5408, 5087], [-1, -20]], [[5407, 5067], [-43, -4]], [[5364, 5063], [-57, 44]], [[5307, 5107], [-72, 47], [-7, 30], [-35, 39], [-8, 49], [-22, 32]], [[5163, 5304], [7, 43]], [[5170, 5347], [-14, 25]], [[5156, 5372], [-24, 23], [-10, 29], [-32, 33], [-30, 28], [-10, -35], [-11, 33], [7, 37]], [[5046, 5520], [17, 56]], [[5063, 5576], [-5, 44]], [[5058, 5620], [18, 45]], [[5076, 5665], [-20, 35]], [[5056, 5700], [5, 64]], [[5061, 5764], [-25, 31]], [[5036, 5795], [-20, 70]], [[5016, 5865], [-11, 75]], [[5005, 5940], [-26, 48], [-40, -29]], [[4939, 5959], [-69, -42]], [[4870, 5917], [-34, 5], [-37, 14], [21, 73], [-13, 55], [-47, 68], [7, 21], [-35, 8]], [[4732, 6161], [-43, 48]], [[4689, 6209], [-18, 31]], [[4671, 6240], [-3, 30], [-12, 28], [-25, 34], [-56, 3]], [[4575, 6335], [6, -25]], [[4581, 6310], [-19, -32]], [[4562, 6278], [-26, 12]], [[4536, 6290], [-9, -11], [-17, 6], [-24, 6]], [[4486, 6291], [-8, -22], [-42, 1]], [[4436, 6270], [-74, -12]], [[4362, 6258], [3, -45], [-32, -35], [-87, -39], [-68, -70], [-46, -37], [-60, -38], [0, -27], [-30, -15], [-55, -21], [-28, -3], [-18, -45], [12, -77], [3, -49]], [[3956, 5757], [-25, -56]], [[3931, 5701], [-1, -100]], [[3930, 5601], [-31, -3]], [[3899, 5598], [-28, -45]], [[3871, 5553], [19, -19], [-55, -17], [-21, -40], [-24, -17]], [[3790, 5460], [-57, 55]], [[3733, 5515], [-29, 83], [-23, 59], [-21, 28], [-32, 57], [-15, 73], [-11, 37], [-55, 81]], [[3547, 5933], [-25, 114]], [[3522, 6047], [-18, 76]], [[3504, 6123], [0, 71], [-12, 55], [-88, -35], [-42, 7]], [[3362, 6221], [-80, 71]], [[3282, 6292], [29, 22]], [[3311, 6314], [-17, 23]], [[3294, 6337], [-71, 50]], [[3223, 6387], [-45, 15], [-18, 42], [-47, 45], [-111, -11], [-99, -1], [-85, -8]], [[2818, 6469], [-114, 17]], [[2704, 6486], [-66, 14]], [[2638, 6500], [-69, 8], [-26, 72], [-29, 10], [-46, -10], [-61, -29], [-74, 20], [-61, 45], [-59, 17], [-40, 56], [-45, 78]], [[2128, 6767], [-33, -9]], [[2095, 6758], [-38, 19]], [[2057, 6777], [-23, -23]], [[2034, 6754], [-36, 3]], [[1998, 6757], [13, -26]], [[2011, 6731], [-6, -13]], [[2005, 6718], [20, -45]], [[2025, 6673], [24, -51], [30, -13], [10, -21], [41, -24], [4, -25]], [[2134, 6539], [-6, -19]], [[2128, 6520], [8, -20], [17, -17], [8, -19], [9, -14]], [[2170, 6450], [-4, 42]], [[2166, 6492], [16, 31]], [[2182, 6523], [17, 7], [18, -19]], [[2217, 6511], [1, -34]], [[2218, 6477], [-13, -35]], [[2205, 6442], [12, -22]], [[2217, 6420], [11, 2]], [[2228, 6422], [2, -16], [47, 10]], [[2277, 6416], [50, -2]], [[2327, 6414], [37, -2]], [[2364, 6412], [41, 40]], [[2405, 6452], [46, 38], [38, 36]], [[2489, 6526], [18, 20]], [[2507, 6546], [7, -5]], [[2514, 6541], [-6, -24], [-8, -11]], [[2500, 6506], [9, -46]], [[2509, 6460], [27, -41], [34, -21], [44, -8], [36, -11], [27, -33]], [[2677, 6346], [17, -20]], [[2694, 6326], [21, -7], [0, -13], [-22, -36], [-9, -16]], [[2684, 6254], [-26, -19]], [[2658, 6235], [-23, -40]], [[2635, 6195], [-27, 3]], [[2608, 6198], [-13, -14]], [[2595, 6184], [-9, -30], [7, -40]], [[2593, 6114], [-6, -7]], [[2587, 6107], [-28, 0]], [[2559, 6107], [-37, -22]], [[2522, 6085], [-6, -28]], [[2516, 6057], [-14, -13]], [[2502, 6044], [-38, 1]], [[2464, 6045], [-24, -15]], [[2440, 6030], [1, -24], [-30, -16], [-33, 5], [-41, -20], [-28, -3]], [[2309, 5972], [-43, -16]], [[2266, 5956], [-12, -26]], [[2254, 5930], [-2, -20]], [[2252, 5910], [-60, -25]], [[2192, 5885], [-97, -27]], [[2095, 5858], [-54, -42]], [[2041, 5816], [-27, -3], [-18, 3], [-35, -24], [-39, -11], [-51, -4], [-15, -3]], [[1856, 5774], [-13, -15]], [[1843, 5759], [-16, -5]], [[1827, 5754], [-10, -15]], [[1817, 5739], [-30, 2], [-19, -8]], [[1768, 5733], [-42, 3]], [[1726, 5736], [-16, 34], [2, 32], [-10, 18], [-12, 43], [-17, 24]], [[1673, 5887], [12, 3]], [[1685, 5890], [-6, 27], [7, 12], [-3, 25]], [[1683, 5954], [-7, 25], [-19, 18]], [[1657, 5997], [-4, 24]], [[1653, 6021], [-32, 21]], [[1621, 6042], [-32, 49]], [[1589, 6091], [-17, 48]], [[1572, 6139], [-42, 41]], [[1530, 6180], [-27, 9], [-40, 57]], [[1463, 6246], [-7, 40]], [[1456, 6286], [2, 35]], [[1458, 6321], [-34, 66]], [[1424, 6387], [-29, 23], [-32, 12], [-20, 34], [3, 13]], [[1346, 6469], [-17, 31]], [[1329, 6500], [-18, 13]], [[1311, 6513], [-23, 43]], [[1288, 6556], [-37, 48]], [[1251, 6604], [-31, 40]], [[1220, 6644], [-30, 0]], [[1190, 6644], [9, 33], [3, 20], [7, 24]], [[1209, 6721], [-2, 8]], [[1207, 6729], [-17, -23], [-13, -45], [-16, -31], [-14, -10], [-21, 19], [-27, 26], [-43, 85], [-6, -6], [25, -62], [37, -59], [46, -92], [22, -32], [20, -33], [54, -65], [-12, -11], [2, -38], [70, -53], [11, -12], [20, -58], [-14, -10], [9, -61], [22, -70], [23, -15], [34, -22], [35, -68], [17, -54], [33, -29], [82, -55], [34, -34], [33, -34], [19, -20], [30, -18], [14, -18], [-2, -24], [-34, -15], [26, -16], [19, -10], [12, -25], [28, -25], [30, 0], [57, 15], [66, 7], [53, 19], [30, 4], [22, 10], [34, 2]], [[2057, 5663], [20, 1], [28, 9]], [[2105, 5673], [32, 6], [28, 20], [23, 1], [2, -17], [-6, -34], [0, -31], [-12, -21], [-18, -64], [-29, -66], [-37, -75], [-52, -86], [-52, -66], [-71, -81], [-61, -47], [-90, -59], [-57, -45], [-66, -71], [-14, -31], [-14, -14], [-42, -24], [-15, -24], [-23, -5], [-9, -41], [-19, -24], [-12, -39], [-24, -19], [-28, -73], [3, -33], [39, -22], [2, -15], [-17, -36], [4, -18], [-4, -28], [21, -37], [25, -58], [22, -13], [10, -26]], [[1544, 4357], [-3, -59], [8, -52]], [[1549, 4246], [2, -92], [11, -29], [-18, -42], [-24, -41], [-38, -36], [-56, -22], [-68, -29], [-68, -63], [-24, -11], [-42, -42], [-25, -14], [-5, -42], [29, -44], [12, -35], [0, -17], [11, 3], [-2, -58], [-9, -28], [14, -10], [-9, -24], [-26, -21], [-50, -20], [-72, -32], [-27, -22], [5, -25], [16, -4], [-5, -31], [-16, -42], [-7, -49], [-15, -27], [-42, -30], [-12, -8], [-25, -30], [-17, -30], [-35, -43], [-68, -60], [-43, -36], [-46, -27], [-63, -22], [-31, -4], [-8, -16], [-37, 9], [-30, -11], [-65, 11], [-37, -7], [-25, 3]], [[459, 2996], [-63, -23], [-51, -10]], [[345, 2963], [-38, -22], [-27, -1], [-26, 21], [-20, 1], [-26, 26], [-3, -8], [-8, 16], [0, 34], [-20, 40], [20, 10], [-2, 46], [-39, 55], [-31, 50], [-43, 76], [-46, 45], [-23, 43], [-13, 55]], [[6723, 4494], [43, 2], [19, 8], [23, -8], [-23, -16], [-63, -25], [-51, -17]], [[6671, 4438], [-39, -44], [-52, -13], [-7, 7], [5, 20], [26, 36], [60, 23]], [[6664, 4467], [7, 14], [52, 13]], [[0, 850], [8, -7], [50, 22], [65, 7], [71, 2], [64, -1], [68, -7], [65, -3], [29, -20], [39, -17], [66, 10], [71, 2], [69, 0], [68, 2], [60, 8], [65, 7], [53, 16], [57, 10], [62, 6], [46, 16], [33, 32], [35, 20], [62, -9], [24, -21], [52, -14], [63, 5], [43, -21], [45, -15], [62, 14], [21, 25], [55, 11], [63, 19], [59, 8], [71, 12], [48, 13], [50, 13], [47, 13], [57, -7], [55, 21], [39, 16], [57, -1], [50, 14], [12, 21], [51, 16], [49, 11], [61, 10], [56, 4], [53, -3], [57, -6], [49, -16], [6, -26], [53, -19], [37, -16], [72, -7], [41, -16], [50, -17], [58, -3], [48, 11], [53, 25], [57, -13], [59, -7], [57, -7], [59, -4], [61, 0], [50, -62], [-3, -15], [-7, -26], [-58, -15], [-48, -22], [9, -23], [67, 1], [-8, -23], [-31, -22], [-28, -24], [46, -19], [70, -6], [70, 11], [33, 23], [21, 22], [33, 18], [38, 18], [15, 20], [32, 29], [38, 6], [69, 2], [61, 7], [61, 10], [30, 23], [18, 22], [41, 21], [60, 15], [51, 12], [33, 20], [34, 10], [44, 9], [61, -5], [54, 5], [60, 7], [66, -3], [44, 16], [31, 39], [23, -16], [28, -28], [51, -11], [58, -5], [58, 7], [62, -5], [57, -1], [38, 6], [51, -3], [46, -13], [55, 8], [65, 0], [56, 8], [63, -8], [40, 20], [31, 19], [42, 16], [76, 44], [39, -8], [46, -16], [40, -21], [78, -36], [59, -1], [56, 0], [65, 7], [65, 8], [50, 16], [42, 18], [67, 2], [45, 13], [48, -12], [31, -18], [43, -19], [66, 3], [42, -15], [72, -15], [76, -6], [63, 4], [47, 19], [41, 18], [54, 5], [55, -8], [63, -6], [57, 9], [54, 0], [54, -5], [55, -6], [55, 10], [65, 9], [62, 3], [69, 0], [56, 6], [54, 4], [17, 29], [2, 24], [38, -16], [11, -26], [20, -25], [25, -19], [51, -11], [69, 4], [79, 1], [55, 3], [80, 0], [57, 2], [79, -3], [68, -4], [42, -19], [-11, -22], [39, -17], [65, -14], [68, -15], [78, -10], [82, -10], [62, -9], [68, -1], [40, 20], [53, -17], [46, -18], [54, -14], [73, -6], [70, -7], [30, -23], [69, -13], [46, -21], [68, -10], [70, 2], [65, -4], [73, 1], [72, -4], [68, -8], [62, -14], [63, -12], [43, -17], [-7, -23], [-32, -21], [-27, -27], [-22, -20], [-28, -25], [-80, -9], [-35, -21], [-79, -12], [-27, -23], [-41, -22], [-44, -19], [-25, -24], [-16, -22], [-6, -26], [1, -22], [35, -24], [13, -21], [28, -21], [113, -8], [24, -26], [-109, -9], [-93, -13], [-115, -2], [-51, -33], [-11, -28], [-26, -22], [-32, -22], [81, -20], [31, -24], [52, -22], [74, -19], [84, -19], [91, -18], [139, -19], [31, -29], [174, -12], [12, -5], [45, -17], [168, 15], [139, -19]], [[9895, 14], [104, -14]], [[9999, 9164], [-66, -3]], [[9933, 9161], [-11, 19], [77, 25]], [[9999, 4016], [-39, -15]], [[9960, 4001], [-38, -12], [-8, 22], [30, 12], [19, 3]], [[9963, 4026], [36, 18]], [[3268, 2126], [40, -18], [57, -8], [2, -11], [-17, -27], [-93, -4], [-2, 32], [9, 24], [4, 12]], [[9885, 3960], [15, 9], [21, -17], [-10, -30], [-37, -8], [-34, 7], [-6, 26], [24, 20], [27, -7]], [[1920, 7619], [60, 61], [59, 10], [28, 35]], [[2067, 7725], [56, 12]], [[2123, 7737], [70, 26]], [[2193, 7763], [52, -14], [60, 2], [11, -36], [-11, -57], [-53, 8], [-51, -9], [-3, -43], [-58, 5]], [[2140, 7619], [2, -19]], [[2142, 7600], [33, -15], [27, -53], [71, -20]], [[2273, 7512], [11, -20]], [[2284, 7492], [-15, -25]], [[2269, 7467], [4, -14]], [[2273, 7453], [19, -38], [6, 43], [49, 15], [17, -34], [44, -35], [-53, -19], [-57, 14], [-14, -49], [40, -4]], [[2324, 7346], [-15, -40]], [[2309, 7306], [47, -20]], [[2356, 7286], [-9, -62]], [[2347, 7224], [12, -41]], [[2359, 7183], [-6, -14], [-95, -16], [-86, 10], [-42, 30]], [[2130, 7193], [-58, 12], [-19, 44]], [[2053, 7249], [-1, 29]], [[2052, 7278], [22, 14], [10, 20], [11, 46], [50, 5], [-19, 16]], [[2126, 7379], [-28, 2]], [[2098, 7381], [-31, 42]], [[2067, 7423], [-32, 31]], [[2035, 7454], [-66, 70], [6, 39], [-55, 56]], [[7567, 9314], [57, 23], [76, 6], [86, -23], [7, -15], [-91, -1], [-125, 7], [-10, 3]], [[8073, 9430], [152, -15], [-70, -23], [-97, 5], [-112, 23], [14, 19], [113, -9]], [[7504, 9477], [160, -3], [219, -31], [-47, -44], [-224, 2], [-100, -14], [-120, 38], [32, 41], [80, 11]], [[5323, 9662], [154, -33], [-18, -24], [-342, -23], [111, 77], [49, 7], [46, -4]], [[4774, 9765], [131, 13], [118, -30], [139, -57], [-15, -53], [-132, -7], [-169, 17], [-100, 23], [-47, 42], [-82, 12], [157, 40]], [[4084, 6783], [20, -3], [63, -31], [18, -13], [19, -7], [20, -12], [43, -1], [136, -7], [8, 6], [12, 5], [39, 0], [146, -2], [64, -3], [70, -11], [22, 2], [45, 12], [28, 14], [20, 15], [7, 1], [4, -5], [3, -8], [-4, -11], [-26, -26], [-1, -8], [11, -19], [2, -17], [19, -13], [-1, -6], [-5, -7], [-28, -19], [-27, -25], [-39, -18], [-65, -10], [-21, -6], [-39, -14], [-26, -2], [-39, 0], [-17, -3], [-13, -6], [-12, -10], [-8, -14], [-10, -40], [6, -21], [2, -43], [15, -12], [14, -14], [9, -18], [7, -18]], [[4792, 6944], [23, 1], [29, -4], [55, -27], [24, -7], [43, -33], [13, -28], [6, -23], [35, -48], [27, -36], [21, -42], [24, -85], [7, -29], [2, -59], [4, -21], [9, -19], [17, -19], [15, -11], [23, -6], [8, -5], [4, -7], [2, -9], [-3, -12], [-21, -41], [-1, -10], [3, -12], [50, -74], [2, -3], [8, 3]], [[5221, 6278], [2, -24]], [[5223, 6254], [-52, -38], [-13, -22]], [[5158, 6194], [26, -18]], [[5184, 6176], [1, -6], [0, -7], [2, 0], [32, -2], [21, 1], [43, 10], [-18, -29], [-7, -49], [-17, -33], [0, -11], [5, -9], [1, -1], [32, 18]], [[5279, 6058], [18, -10], [36, 1], [12, 21], [46, -4], [46, -48], [4, -58], [49, -52]], [[5490, 5908], [-1, -14], [12, -12], [7, -21], [-1, -32], [5, -22], [0, -29], [1, -41], [-3, -16], [-7, -6], [-10, -3], [-17, -16], [-19, -9], [-6, -13], [10, -39], [6, -13], [21, -26], [46, -47]], [[4565, 7798], [-8, -14], [-28, -14], [-21, -6], [-21, 1], [-127, 34], [-33, 12], [-69, 8], [-55, -16], [-32, 18], [-13, 10], [-4, 7], [-3, 27], [7, 7], [15, 7], [9, 9], [-8, 10], [-31, 18], [-53, 24], [-21, 8], [-22, 4], [-82, 8], [-114, 22], [-79, 65], [-15, 15], [-24, 34], [-17, 12], [-44, 23], [-17, 12], [-12, 11], [-4, 7]], [[4593, 7012], [22, 1], [111, -13], [52, 13], [19, 14], [14, 6], [18, 1], [19, -4], [23, -12], [38, -31], [24, -12], [38, -26], [25, -27], [44, -29], [13, -16], [10, -19], [14, -19], [-5, -13], [13, -28], [4, -19], [7, -65], [8, -48], [3, -4], [5, -9], [3, -5], [2, -6], [5, -14], [4, -13], [2, -7], [11, -16], [11, -5], [10, 9], [6, 15], [7, 10], [6, -6], [3, -12], [-4, -33], [0, -25], [4, -8], [6, -1], [24, 2], [40, 16], [9, 0], [4, -7], [2, -11], [5, -7], [27, 10], [19, 2], [9, 5], [5, 12], [-2, 37], [4, 9], [8, 3], [19, 27], [5, 15], [15, 11], [3, 9], [0, 11], [29, 0], [25, 7], [23, -1], [26, 7], [17, -1], [5, 8], [43, 32], [60, 22], [17, 13], [25, 26], [6, 9], [11, 7], [17, 5], [36, 4], [49, 1], [34, -6], [24, -11], [19, -21], [30, -2], [11, -7], [14, -24], [9, -5], [12, 0], [8, -2], [2, -9], [11, -5], [32, 25], [16, 10], [0, 9], [7, 5], [16, 18], [14, 0], [15, -5], [37, -30], [17, -9], [18, -3], [18, 2], [26, 12], [15, 22], [23, 16], [16, 8], [17, 17], [24, 16], [17, 26], [12, 12], [16, 7], [20, 3], [25, -4], [50, -30], [21, -6], [57, -6]], [[6133, 6744], [0, 2]], [[7636, 4838], [1, -192], [1, -192]], [[1145, 7068], [5, -11]], [[1150, 7057], [2, -5]], [[1046, 7059], [29, 2]], [[1075, 7061], [4, 1]], [[6993, 7482], [-15, -12], [-11, -8]], [[6951, 7400], [-3, -1], [-28, -12]], [[6920, 7387], [-7, -7], [-4, -4]], [[6820, 7333], [-2, -15], [0, -10]], [[6818, 7308], [-3, -3], [-4, -4]], [[6811, 7301], [23, -9], [1, 0]], [[6835, 7292], [8, -7], [26, -19]], [[6869, 7266], [1, -1], [51, -69]], [[6756, 7017], [-5, 24], [-2, 8]], [[6749, 7049], [6, 21], [5, 23]], [[6734, 7155], [15, 3], [30, 7]], [[6779, 7165], [-28, 34], [-14, 16]], [[6737, 7215], [-5, 2], [-24, 9]], [[6708, 7226], [-5, -7], [-3, -4]], [[6700, 7215], [-5, -1], [-12, -4]], [[6679, 7269], [0, 1], [-5, 10]], [[6674, 7280], [4, 7], [11, 25]], [[6685, 7321], [-23, 5], [-12, 2]], [[6650, 7328], [-13, 7], [-15, 9]], [[6622, 7344], [-85, -18]], [[6537, 7326], [-11, -7], [-34, -20]], [[6492, 7299], [-32, -8], [-33, -8]], [[6410, 7383], [-44, -26], [-25, -15]], [[6304, 7304], [-34, -2], [-26, -1]], [[6245, 7234], [36, -7], [14, -3]], [[6413, 7222], [40, -17], [14, -6]], [[6467, 7199], [19, 0], [20, -1]], [[6506, 7198], [1, -3], [9, -28]], [[6516, 7167], [-56, -11], [-30, -6]], [[6478, 6814], [-22, -8], [-16, -8]], [[6440, 6798], [7, -15], [7, -16]], [[6490, 6749], [-3, -14], [-7, -34]], [[6431, 6649], [-9, -14], [-35, -50]], [[6282, 6437], [-52, -33], [-32, -21]], [[6198, 6383], [-31, -19], [-53, -31]], [[6046, 6327], [-10, -7], [-27, -20]], [[5988, 6320], [-18, -16], [-17, -14]], [[5953, 6290], [-62, -21], [-22, -8]], [[5869, 6261], [-13, -2], [-51, -7]], [[5805, 6252], [-21, -63]], [[5558, 6210], [-35, -38], [-16, -17]], [[5540, 6052], [30, -40], [27, -37]], [[5597, 5975], [2, -1], [53, -36]], [[5689, 5891], [9, -36], [19, -73]], [[5717, 5782], [-4, -47], [-4, -57]], [[5539, 5553], [-23, -17], [-52, -38]], [[5414, 5609], [-51, 9], [-25, 30]], [[5253, 5736], [-47, -1], [-5, 0]], [[5210, 5781], [-2, 0], [-51, 0]], [[5157, 5781], [-3, -39], [-2, -26]], [[5100, 5578], [2, -22], [2, -21]], [[5179, 5429], [24, -24], [10, -10]], [[5213, 5395], [2, 0], [34, -7]], [[5359, 5237], [-5, -22], [-1, -4]], [[5353, 5211], [2, -7], [4, -14]], [[5408, 5087], [0, -3], [-1, -17]], [[5407, 5067], [-30, -2], [-13, -2]], [[5364, 5063], [-6, 5], [-51, 39]], [[5163, 5304], [3, 23], [4, 20]], [[5046, 5520], [12, 38], [5, 18]], [[5063, 5576], [0, 2], [-5, 42]], [[5058, 5620], [15, 38], [3, 7]], [[5056, 5700], [1, 22], [4, 42]], [[5036, 5795], [-16, 57], [-4, 13]], [[5016, 5865], [-3, 22], [-8, 53]], [[4939, 5959], [-14, -9], [-55, -33]], [[4732, 6161], [-41, 45], [-2, 3]], [[4689, 6209], [-4, 5], [-14, 26]], [[4581, 6310], [-8, -12], [-11, -20]], [[4562, 6278], [-21, 9], [-5, 3]], [[4436, 6270], [-47, -8], [-27, -4]], [[3956, 5757], [-3, -6], [-22, -50]], [[3931, 5701], [0, -38], [-1, -62]], [[3899, 5598], [-3, -5], [-25, -40]], [[3790, 5460], [0, 1], [-57, 54]], [[3547, 5933], [-24, 109], [-1, 5]], [[3522, 6047], [-2, 7], [-16, 69]], [[3362, 6221], [-66, 59], [-14, 12]], [[3282, 6292], [1, 1], [28, 21]], [[3311, 6314], [-6, 8], [-11, 15]], [[2818, 6469], [-82, 12], [-32, 5]], [[2704, 6486], [-13, 3], [-53, 11]], [[2128, 6767], [-28, -8], [-5, -1]], [[2095, 6758], [-17, 8], [-21, 11]], [[2057, 6777], [-10, -10], [-13, -13]], [[2011, 6731], [-3, -8], [-3, -5]], [[2005, 6718], [14, -32], [6, -13]], [[2134, 6539], [-4, -13], [-2, -6]], [[2170, 6450], [-3, 31], [-1, 11]], [[2166, 6492], [5, 10], [11, 21]], [[2217, 6511], [1, -26], [0, -8]], [[2218, 6477], [-3, -10], [-10, -25]], [[2217, 6420], [3, 0], [8, 2]], [[2277, 6416], [44, -2], [6, 0]], [[2327, 6414], [2, 0], [35, -2]], [[2364, 6412], [7, 7], [34, 33]], [[2507, 6546], [4, -3], [3, -2]], [[2677, 6346], [13, -15], [4, -5]], [[2684, 6254], [-8, -6], [-18, -13]], [[2658, 6235], [-13, -23], [-10, -17]], [[2608, 6198], [-10, -12], [-3, -2]], [[2593, 6114], [-2, -3], [-4, -4]], [[2587, 6107], [-1, 0], [-27, 0]], [[2559, 6107], [-36, -21], [-1, -1]], [[2516, 6057], [-11, -10], [-3, -3]], [[2464, 6045], [-15, -10], [-9, -5]], [[2266, 5956], [-8, -17], [-4, -9]], [[2254, 5930], [0, -3], [-2, -17]], [[2192, 5885], [-28, -8], [-69, -19]], [[2095, 5858], [-48, -38], [-6, -4]], [[1856, 5774], [-8, -9], [-5, -6]], [[1843, 5759], [-11, -3], [-5, -2]], [[1827, 5754], [-4, -6], [-6, -9]], [[1768, 5733], [-17, 1], [-25, 2]], [[1673, 5887], [3, 1], [9, 2]], [[1657, 5997], [-4, 20], [0, 4]], [[1653, 6021], [-8, 5], [-24, 16]], [[1589, 6091], [-12, 33], [-5, 15]], [[1572, 6139], [-16, 15], [-26, 26]], [[1463, 6246], [-6, 36], [-1, 4]], [[1458, 6321], [-3, 6], [-31, 60]], [[1346, 6469], [-5, 8], [-12, 23]], [[1311, 6513], [-17, 31], [-6, 12]], [[1288, 6556], [-11, 14], [-26, 34]], [[1220, 6644], [-19, 0], [-11, 0]], [[1207, 6729], [-6, 15], [-34, 86]], [[1167, 6830], [6, 6], [12, 14]], [[1181, 6853], [0, 1], [16, 27]], [[1209, 6925], [2, 4], [7, 11]], [[1218, 6940], [1, 1], [1, 0]], [[1245, 7145], [-26, 7], [-24, 7]], [[1153, 7125], [-55, -4], [-37, -3]], [[1061, 7118], [-1, 1], [-48, 31]], [[947, 7152], [-7, -11], [-7, -13]], [[933, 7128], [-26, -5], [-16, -2]], [[891, 7121], [-14, 7], [-45, 24]], [[686, 7242], [13, 21], [16, 25]], [[677, 7316], [31, 26], [36, 30]], [[1538, 7407], [19, 9], [52, 22]], [[1609, 7438], [2, 5], [7, 20]], [[1603, 7504], [-12, 7], [-23, 14]], [[1512, 7550], [8, 7], [51, -10], [89, -10], [83, -28], [10, -11], [37, 10], [56, -13], [19, -24], [38, -14]], [[1903, 7457], [17, -2], [42, -35], [27, -4], [10, 15], [36, 23]], [[2067, 7423], [11, -14], [20, -28]], [[2098, 7381], [7, 0], [21, -2]], [[2052, 7278], [0, -9], [1, -20]], [[2359, 7183], [-8, 29], [-4, 12]], [[2356, 7286], [-2, 1], [-45, 19]], [[2309, 7306], [1, 2], [14, 38]], [[2273, 7453], [-3, 10], [-1, 4]], [[2284, 7492], [-2, 4], [-9, 16]], [[2142, 7600], [-1, 2], [-1, 17]], [[2193, 7763], [-19, -7], [-51, -19]], [[2067, 7725], [-31, 9], [6, 31], [-39, 39], [-45, -2], [-51, 40], [35, 45], [-18, 12], [48, 65], [63, -34], [7, 43], [125, 64], [95, 1], [134, -41], [71, -23], [65, 24], [96, 2], [77, -31], [18, 18], [85, -3], [15, 28], [-98, 40], [58, 29], [-11, 16], [58, 15], [-44, 41], [28, 20], [227, 20], [29, 15], [152, 22], [55, 24], [108, -13], [20, -61], [63, 15], [78, -20], [-5, -32], [58, 3], [152, 55], [-23, -18], [78, -46], [135, -149], [32, 31], [84, -34], [87, 15], [33, -11], [30, -34], [42, -11], [26, -25], [78, 8], [32, -36]], [[4385, 7891], [24, 4]], [[4409, 7895], [64, 11], [115, 50], [92, 28], [53, -18], [63, -1], [41, -27], [60, -2], [88, -15], [59, 41], [-25, 34], [63, 62], [68, -25], [55, -7], [71, -15], [12, -44], [86, -25], [57, 11], [77, 8], [60, -8], [60, -28], [37, -30], [56, 0], [76, -9], [56, 14], [80, 10], [88, 42], [37, -7], [31, -20], [73, 5]], [[6162, 7930], [72, -22], [86, 37], [-1, 26], [55, 63], [34, 19], [-1, 32], [-33, 14], [50, 29], [75, 11], [80, 2], [91, -18], [53, -22], [38, -59], [23, -25], [21, -37], [22, -57], [106, -19], [71, -42], [25, -55], [92, 0], [52, 23], [101, 17], [-32, -53], [-24, -21], [-21, -65], [-40, -57], [-74, 10], [-52, -21], [16, -50], [-9, -70], [-31, -1], [1, -30]], [[7008, 7489], [8, -10]], [[837, 7410], [-47, -2], [-25, -1]], [[688, 7357], [-10, 15], [-9, 12]], [[670, 7396], [14, 7], [19, 37], [-29, 15], [61, 19], [53, -8]], [[788, 7466], [3, -11], [4, -12]], [[2130, 7193], [-57, 12], [-20, 44]], [[9933, 9161], [-21750, 3], [21816, 41]], [[9420, 9121], [-26, -26], [49, -38]], [[9367, 9038], [-105, 53], [-115, -7]], [[2451, 9023], [-44, -20], [-75, 6]], [[2346, 9048], [-211, -51], [-127, -28]], [[1724, 9031], [45, -37], [-30, -35]], [[1344, 8829], [-40, -21], [38, -27]], [[1209, 8786], [-8, 88]], [[0, 8993], [0, 536]], [[0, 9685], [0, 314], [-11817, 0], [0, -945], [21731, 26]], [[477, 8517], [99, 12], [106, 22]], [[0, 8179], [0, 122]], [[0, 7469], [0, 154]], [[506, 7345], [33, 1], [31, 9]], [[0, 6897], [0, 254]], [[0, 7240], [0, 118]], [[9960, 4001], [-21777, 15], [0, 28], [21780, -18]], [[0, 850], [0, 2600]], [[345, 2963], [52, 10], [62, 23]], [[1549, 4246], [-7, 52], [2, 59]], [[2105, 5673], [-28, -8], [-20, -2]], [[6622, 7344], [28, -16]], [[8890, 8248], [26, 50], [56, 2]], [[9921, 8793], [-21738, 26], [0, -8819], [21712, 14]], [[3878, 7745], [15, -6], [-21, -16], [-27, -4], [-75, 6], [-97, 6], [-9, 10], [-3, -12], [-30, -4], [-9, -17], [-30, -7], [-11, -59], [-40, 36], [0, 12], [18, 12], [-3, 10], [21, 1], [4, 13], [51, 23], [77, -2], [59, -8], [41, 0], [27, -10], [6, 11], [36, 5]], [[2648, 7672], [5, 23], [26, 3], [20, -14], [1, 35], [13, -3], [21, -9], [0, -12], [-10, -5], [-7, -17], [17, 3], [8, -46]], [[2742, 7630], [-12, -31], [-17, -6], [-43, 12], [17, 37], [1, 12], [-24, 10], [-2, -16], [0, -21], [-26, -31], [-13, 15], [0, 25], [25, 36]], [[4385, 7891], [-46, -40], [-50, -5], [-3, -59], [-34, -27], [-120, 20], [-44, -106], [-31, -13], [-120, -24], [54, -102], [-41, -15], [5, -34]], [[3955, 7486], [-38, 9], [-30, 21], [-90, 6], [-100, 2], [-22, -7], [-87, 25], [-34, -12], [-10, -35], [-99, 20], [-40, -8], [-14, -26]], [[3391, 7481], [-34, -11], [-80, -41], [-27, -42], [-22, 0], [-17, 28], [-77, 2], [-12, 48], [-30, 0], [5, 59], [-73, 43], [-104, -4], [-71, -9], [-58, 53], [-49, 23]], [[2742, 7630], [-94, 42]], [[2648, 7672], [-12, 5], [-156, -35], [3, -217]], [[2483, 7425], [-31, -3], [-43, 46], [-41, 17], [-69, -13], [-26, -19]], [[3391, 7481], [18, -6], [-51, -38], [45, -22], [43, 15], [72, -31], [-77, -43], [-47, 6]], [[3394, 7362], [-25, -1], [-8, 16], [12, 27], [-81, -13], [-19, -38], [-29, -33], [-50, 3], [-16, -26], [44, -14], [14, -44], [-35, -59]], [[3201, 7180], [-45, 12], [-34, 0]], [[3122, 7192], [2, 36], [-81, 26], [-63, 28], [-40, 28], [-69, 41], [-30, 61], [-20, 10], [-66, -2], [-23, 12], [-6, 47], [-82, 31], [-51, -34], [-52, -21], [10, -29], [-68, -1]], [[6664, 4467], [6, -11], [1, -18]], [[5737, 5109], [10, -39], [41, -33], [39, 12], [39, -5], [35, 30], [29, 5], [58, -16], [49, 12], [31, 82], [23, 21], [21, 67], [70, 0], [53, -10]], [[1256, 6918], [-11, -18]], [[1245, 6900], [-22, 8], [-13, -39], [16, -7], [-16, -8], [-2, -16], [28, 8]], [[1236, 6846], [2, -23], [-31, -94]], [[1220, 6941], [20, 0], [5, 10], [17, 1]], [[1262, 6952], [1, -24], [-9, -9], [2, -1]], [[1273, 7032], [27, -3], [10, -23], [-33, -22], [-15, -32]], [[1245, 6900], [0, -36], [-9, -18]], [[1256, 6918], [67, -23], [119, 63]], [[1442, 6958], [24, -72]], [[1466, 6886], [-11, -9], [-122, -30], [61, -58], [-20, -10], [-10, -20], [-47, -8], [-14, -22], [-26, -18], [-68, 10]], [[2489, 6526], [11, -20]], [[2509, 6460], [-31, -1], [-5, -38], [10, -8], [-27, -12], [0, -24], [-18, -24], [-1, -24]], [[2437, 6329], [-13, -12], [-182, 29], [-23, 60], [-2, 14]], [[2205, 6442], [-17, -4], [-18, 12]], [[2025, 6673], [-43, -1], [-15, 28], [-54, 5]], [[1913, 6705], [45, 57], [40, -5]], [[1442, 6958], [134, 61], [23, 71], [-6, 43], [33, 15], [31, 37]], [[1657, 7185], [26, 9], [71, -8], [21, -15], [29, 10]], [[1804, 7181], [39, -70], [40, -18], [5, -34], [-31, -21], [-14, -46], [42, -56], [74, -32], [32, -45], [-10, -42], [19, 0], [1, -32], [33, -31]], [[1913, 6705], [-113, 5], [-170, 119], [-91, 41], [-73, 16]], [[2309, 5972], [-19, 41], [-48, 97]], [[2242, 6110], [182, 59], [40, 118], [-27, 42]], [[5308, 5709], [-15, 71], [39, 49], [78, 11], [57, -8]], [[5467, 5832], [50, -23], [28, 41], [53, -22]], [[5598, 5828], [14, -39], [-7, -71], [-102, -45], [27, -36], [-64, -4], [-52, -24]], [[5281, 5358], [-20, -25], [-40, -7], [-5, 31], [-49, 25], [-11, -10]], [[5063, 5576], [30, 61], [33, 55], [-24, 53], [1, 28], [-7, 33], [-40, 47], [-15, 29], [21, 11], [23, 51], [-25, 39], [-39, 43], [-29, 52], [26, 11], [27, 63], [43, 3], [35, 25], [35, 14]], [[5184, 6176], [4, -36], [41, -2], [-15, -62], [1, -53], [64, 35]], [[5490, 5908], [-3, -49], [-20, -27]], [[5223, 6254], [5, -14], [32, -1], [-9, 67], [32, 9]], [[5283, 6315], [35, -47], [27, -53], [75, -1], [23, -51], [-38, -16], [-18, -21], [73, -35], [50, -70], [38, -52], [46, -41], [15, -41], [-11, -59]], [[4689, 6209], [-4, 47], [21, -9], [1, 43]], [[4707, 6290], [30, 14], [-7, 25], [14, 20], [3, 61], [47, -14], [27, 49], [3, 29], [33, 49], [-1, 34], [78, 40], [43, -10], [-5, 36], [22, 11], [-5, 22]], [[4989, 6656], [35, 5], [21, -35], [26, -14], [2, -45], [-2, -49], [-58, -49], [-7, -70], [64, 10], [14, -54], [39, -12], [-18, -49], [45, -22], [26, -11], [45, 17]], [[5283, 6315], [32, 14], [48, 0], [59, 6], [52, 32], [29, -22], [56, -11], [-10, -34], [29, -24], [61, -15]], [[7016, 7479], [0, 0]], [[6869, 7266], [-9, -14], [-26, -4], [-42, -3], [-24, -27], [-27, 2], [-4, -5]], [[6621, 7344], [50, 37], [67, 32], [41, 42], [29, -19], [52, -2], [-9, 31], [94, 26], [24, 33], [39, -35]], [[6162, 7930], [-30, -44], [-43, -59], [16, -24], [34, 7], [60, -9], [47, 22], [48, -19], [55, -41], [-6, -21], [-48, 7], [-88, -8], [-43, -17], [-44, -39], [-93, -23], [-60, -31], [-62, 12], [-34, 5], [-32, -38], [19, -22], [10, -20], [-43, -20], [-43, -31], [-71, -21], [-91, -2], [-98, -21], [-70, -31], [-27, 18], [-73, 0], [-90, 36], [-60, 9], [-80, -9], [-125, 14], [-67, -2], [-36, 35], [-27, 55], [-38, 6], [-73, 37], [-82, 8], [-72, 10], [-22, 26], [24, 69], [-42, 47], [-86, 22], [-51, 31], [-16, 41]], [[4707, 6290], [-32, 93], [-17, 0], [-10, -38], [-33, 31], [19, 33], [27, 4], [28, 50], [-35, 10], [-56, -1], [-58, 8], [-5, 41], [-29, 3], [-48, 25], [-21, -40], [43, -31], [-38, -22], [-13, -21], [37, -16], [-10, -36], [21, -44], [9, -48]], [[3223, 6387], [40, 39], [133, 0], [-12, 51], [-34, 30], [-7, 45], [-39, 26], [66, 62], [71, -4], [63, 61], [38, 60], [59, 59], [-1, 42], [52, 34], [-49, 29], [-21, 40], [-22, 52], [30, 25], [92, -14], [67, 9], [59, 49]], [[3808, 7082], [65, -69], [-6, -48], [24, -30], [-2, -30], [-43, 8], [17, -65], [59, -37], [84, -42]], [[4006, 6769], [-38, -26], [-24, -55], [59, -23], [57, -29], [79, -33], [83, -7], [35, -30], [47, -6], [73, -14], [50, 1], [7, 24], [-8, 37], [5, 25]], [[4431, 6633], [37, 13], [5, -47]], [[4473, 6599], [1, -11], [56, -23], [38, 9], [51, -3], [49, 1], [4, 36], [-24, 19]], [[4648, 6627], [49, 8], [55, 43], [70, 38], [51, -14], [43, 24], [28, -36], [-20, -25], [65, -9]], [[4473, 6599], [40, 44], [33, 15], [43, -13], [32, -2], [27, -16]], [[4006, 6769], [25, 14], [49, -18], [61, -38], [34, -8], [21, -29], [47, -11], [49, -26], [68, -14], [71, -6]], [[2818, 6469], [23, 68], [87, 30], [-5, 28], [-29, 9], [-2, 52], [-58, 26], [-24, 36], [-30, 31]], [[2780, 6749], [102, -31], [60, 9], [36, -7], [13, 13], [42, -6], [79, 25], [2, 50], [33, 33], [46, 0], [6, 17], [47, 8], [22, -6], [24, 17], [-4, 35], [26, 36], [39, 14], [-24, 39], [57, -1], [17, 21], [-2, 22], [30, 25], [-7, 29], [-15, 25], [36, 26], [65, 12], [69, 7], [31, 11], [36, 7]], [[3646, 7179], [44, -28], [18, -45], [100, -24]], [[3201, 7180], [19, -8], [44, 19], [20, -11], [20, 27], [36, -1], [9, 8], [7, 24], [26, 21], [33, -14], [-7, -18], [18, -3], [-5, -49], [24, -19], [21, 12], [27, 6], [38, 26], [41, -4], [63, 0]], [[3635, 7196], [11, -17]], [[2780, 6749], [55, 53], [-5, 38], [-46, 10], [-5, 37], [-20, 47], [26, 32], [-26, 9], [17, 43], [24, 73]], [[2800, 7091], [62, -22], [46, 8], [13, 27], [47, 9], [35, 17], [12, 48], [51, 11], [9, 21], [29, -16], [18, -2]], [[3394, 7362], [-22, -18], [-66, 10], [-5, -34], [65, 4], [75, -19], [115, 9]], [[3556, 7314], [15, -54], [20, 6], [37, -14], [-2, -23], [9, -33]], [[3955, 7486], [-9, -13], [-95, -32], [-22, -23], [-78, -7], [-23, -38], [-64, 8], [-42, -12], [-57, -28], [8, -13], [-17, -14]], [[2800, 7091], [-5, 50], [-45, 2], [-69, 52], [-49, 7], [-67, 30], [-43, 5], [-26, -11], [-41, 2], [-43, -34], [-53, -11]], [[1804, 7181], [-33, 47], [12, 18], [-19, 68], [42, 17]], [[1806, 7331], [9, -22], [31, -28], [41, -7]], [[1887, 7274], [22, 1]], [[1909, 7275], [72, 44], [22, 4], [18, -17], [-21, -29], [38, -31], [15, 3]], [[1282, 7102], [16, 12], [16, 13], [3, 33], [20, -11], [67, 16], [32, -11], [50, 0], [70, 22], [32, -1], [69, 10]], [[1887, 7274], [-24, 34], [0, 9], [-27, 0], [-18, 15], [-12, -1]], [[1806, 7331], [-24, 17], [-45, 15], [5, 28], [-10, 21]], [[1732, 7412], [84, 9]], [[1816, 7421], [13, -15], [23, -10], [-12, -15], [32, -20], [-17, -19], [26, -16], [27, -10], [1, -41]], [[1609, 7438], [65, 3], [58, -29]], [[1816, 7421], [15, 10], [45, -17], [33, -4], [8, 7], [-30, 32], [16, 8]], [[6012, 5258], [27, -31], [13, 20], [29, -2], [4, 38], [2, 29]], [[1683, 5954], [27, 19], [-6, 25], [16, 29], [25, -16], [16, 6], [70, 1], [11, -6], [59, -6], [23, 3], [15, -19], [28, 10], [44, 61], [56, 27], [175, 22]], [[1150, 7057], [-7, 2], [-11, -5], [-9, 1], [-3, -2], [-2, 6], [-4, 4], [-12, 0], [-16, -5], [-11, 3]]], "transform": {"scale": [0.0165016501650165, 0.01697303530353035], "translate": [15, -84.71338]}, "bbox": [15, -84.71338, 180, 85]}