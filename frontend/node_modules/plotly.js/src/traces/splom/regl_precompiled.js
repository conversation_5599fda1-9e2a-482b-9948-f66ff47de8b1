'use strict';
var v0 = require('../../generated/regl-codegen/3e771157d23b4793771f65d83e6387262ed73d488209157f19a7fa027bddd71b');
var v1 = require('../../generated/regl-codegen/cbf700f001fff25b649fba9c37fa0dc6631c1cdee318ad49473d28ec10dcee81');
var v2 = require('../../generated/regl-codegen/8fad2284703471df7c0e0d0a7b96d983e8c53f6d707dd55d5921c1eab71f6623');
var v3 = require('../../generated/regl-codegen/dbd1cc9126a137a605df67dc0706e55116f04e33b4545a80042031752de5aef5');
var v4 = require('../../generated/regl-codegen/bfc540da96a87fcc039073cb37b45e6b81ef5ee6ef3529d726ceed8336354019');
var v5 = require('../../generated/regl-codegen/fe5b6844077cde1bdd7273f4495969fad93500c26a69b62e74ec2664c447bcc7');
var v6 = require('../../generated/regl-codegen/db1b82c68771e7f5012fad1fbdae7ff23b526e58d2995bf6dd2cf30024e0f41d');
var v7 = require('../../generated/regl-codegen/49e82bba439f1d9d441c17ba252d05640bc63fefdf22d1219993633af7730210');
var v8 = require('../../generated/regl-codegen/6a5d6bd29c15cf7614221b94c3f384df47c2c46fbe4456e8c57b5cd14c84d923');
var v9 = require('../../generated/regl-codegen/8902aff2b23b600f8103bcc84a8af2999d28795208aedadc2db06f921f9c7034');

/* eslint-disable quote-props */
module.exports = {
    '3e771157d23b4793771f65d83e6387262ed73d488209157f19a7fa027bddd71b': v0,
    'cbf700f001fff25b649fba9c37fa0dc6631c1cdee318ad49473d28ec10dcee81': v1,
    '8fad2284703471df7c0e0d0a7b96d983e8c53f6d707dd55d5921c1eab71f6623': v2,
    'dbd1cc9126a137a605df67dc0706e55116f04e33b4545a80042031752de5aef5': v3,
    'bfc540da96a87fcc039073cb37b45e6b81ef5ee6ef3529d726ceed8336354019': v4,
    'fe5b6844077cde1bdd7273f4495969fad93500c26a69b62e74ec2664c447bcc7': v5,
    'db1b82c68771e7f5012fad1fbdae7ff23b526e58d2995bf6dd2cf30024e0f41d': v6,
    '49e82bba439f1d9d441c17ba252d05640bc63fefdf22d1219993633af7730210': v7,
    '6a5d6bd29c15cf7614221b94c3f384df47c2c46fbe4456e8c57b5cd14c84d923': v8,
    '8902aff2b23b600f8103bcc84a8af2999d28795208aedadc2db06f921f9c7034': v9
};
