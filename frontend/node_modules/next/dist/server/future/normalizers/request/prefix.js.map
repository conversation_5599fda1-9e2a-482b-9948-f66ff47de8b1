{"version": 3, "sources": ["../../../../../src/server/future/normalizers/request/prefix.ts"], "names": ["PrefixPathnameNormalizer", "constructor", "prefix", "endsWith", "Error", "match", "pathname", "startsWith", "normalize", "matched", "length", "substring"], "mappings": ";;;;+BAEaA;;;eAAAA;;;AAAN,MAAMA;IACXC,YAAY,AAAiBC,MAAc,CAAE;aAAhBA,SAAAA;QAC3B,IAAIA,OAAOC,QAAQ,CAAC,MAAM;YACxB,MAAM,IAAIC,MACR,CAAC,kCAAkC,EAAEF,OAAO,6BAA6B,CAAC;QAE9E;IACF;IAEOG,MAAMC,QAAgB,EAAE;QAC7B,iEAAiE;QACjE,IAAIA,aAAa,IAAI,CAACJ,MAAM,IAAI,CAACI,SAASC,UAAU,CAAC,IAAI,CAACL,MAAM,GAAG,MAAM;YACvE,OAAO;QACT;QAEA,OAAO;IACT;IAEOM,UAAUF,QAAgB,EAAEG,OAAiB,EAAU;QAC5D,uEAAuE;QACvE,IAAI,CAACA,WAAW,CAAC,IAAI,CAACJ,KAAK,CAACC,WAAW,OAAOA;QAE9C,IAAIA,SAASI,MAAM,KAAK,IAAI,CAACR,MAAM,CAACQ,MAAM,EAAE;YAC1C,OAAO;QACT;QAEA,OAAOJ,SAASK,SAAS,CAAC,IAAI,CAACT,MAAM,CAACQ,MAAM;IAC9C;AACF"}