{"version": 3, "sources": ["../../../../src/server/future/route-matcher-managers/default-route-matcher-manager.ts"], "names": ["DefaultRouteMatcherManager", "compilationID", "providers", "length", "waitTillReady", "waitTillReadyPromise", "reload", "promise", "resolve", "reject", "Detached<PERSON>romise", "matchers", "providersMatchers", "Promise", "all", "map", "provider", "Map", "duplicates", "providerMatchers", "matcher", "duplicated", "duplicate", "get", "definition", "pathname", "others", "push", "set", "previousMatchers", "every", "cachedMatcher", "index", "static", "filter", "isDynamic", "dynamic", "reference", "pathnames", "Array", "indexes", "sorted", "getSortedRoutes", "sortedDynamicMatchers", "isArray", "Error", "dynamicMatches", "err", "lastCompilationID", "test", "options", "match", "matchAll", "validate", "LocaleRouteMatcher", "i18n", "inferredFromDefault", "ensureLeadingSlash", "isDynamicRoute", "skipDynamic"], "mappings": ";;;;+BAkBaA;;;eAAAA;;;uBAlBkB;oCAQI;oCACA;iCACH;AAQzB,MAAMA;IASX;;;GAGC,GACD,IAAYC,gBAAgB;QAC1B,OAAO,IAAI,CAACC,SAAS,CAACC,MAAM;IAC9B;IAGA,MAAaC,gBAA+B;QAC1C,IAAI,IAAI,CAACC,oBAAoB,EAAE;YAC7B,MAAM,IAAI,CAACA,oBAAoB;YAC/B,OAAO,IAAI,CAACA,oBAAoB;QAClC;IACF;IAGA,MAAaC,SAAS;QACpB,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAE,GAAG,IAAIC,gCAAe;QACxD,IAAI,CAACL,oBAAoB,GAAGE;QAE5B,sEAAsE;QACtE,yEAAyE;QACzE,gBAAgB;QAChB,MAAMN,gBAAgB,IAAI,CAACA,aAAa;QAExC,IAAI;YACF,+CAA+C;YAC/C,MAAMU,WAAgC,EAAE;YAExC,kCAAkC;YAClC,MAAMC,oBACJ,MAAMC,QAAQC,GAAG,CAAC,IAAI,CAACZ,SAAS,CAACa,GAAG,CAAC,CAACC,WAAaA,SAASL,QAAQ;YAEtE,0CAA0C;YAC1C,MAAMG,MAAM,IAAIG;YAChB,MAAMC,aAA6C,CAAC;YACpD,KAAK,MAAMC,oBAAoBP,kBAAmB;gBAChD,KAAK,MAAMQ,WAAWD,iBAAkB;oBACtC,wEAAwE;oBACxE,IAAIC,QAAQC,UAAU,EAAE,OAAOD,QAAQC,UAAU;oBACjD,yDAAyD;oBACzD,MAAMC,YAAYR,IAAIS,GAAG,CAACH,QAAQI,UAAU,CAACC,QAAQ;oBACrD,IAAIH,WAAW;wBACb,6DAA6D;wBAC7D,+DAA+D;wBAC/D,gEAAgE;wBAChE,mEAAmE;wBACnE,+DAA+D;wBAC/D,mEAAmE;wBACnE,2DAA2D;wBAC3D,iEAAiE;wBACjE,oEAAoE;wBACpE,8DAA8D;wBAC9D,oEAAoE;wBACpE,kDAAkD;wBAClD,MAAMI,SAASR,UAAU,CAACE,QAAQI,UAAU,CAACC,QAAQ,CAAC,IAAI;4BACxDH;yBACD;wBACDI,OAAOC,IAAI,CAACP;wBACZF,UAAU,CAACE,QAAQI,UAAU,CAACC,QAAQ,CAAC,GAAGC;wBAE1C,wCAAwC;wBACxCJ,UAAUD,UAAU,GAAGK;wBACvBN,QAAQC,UAAU,GAAGK;oBAErB,6DAA6D;oBAC/D;oBAEAf,SAASgB,IAAI,CAACP;oBAEd,yCAAyC;oBACzCN,IAAIc,GAAG,CAACR,QAAQI,UAAU,CAACC,QAAQ,EAAEL;gBACvC;YACF;YAEA,yEAAyE;YACzE,4BAA4B;YAC5B,IAAI,CAACT,QAAQ,CAACO,UAAU,GAAGA;YAE3B,uEAAuE;YACvE,wEAAwE;YACxE,iEAAiE;YACjE,IACE,IAAI,CAACW,gBAAgB,CAAC1B,MAAM,KAAKQ,SAASR,MAAM,IAChD,IAAI,CAAC0B,gBAAgB,CAACC,KAAK,CACzB,CAACC,eAAeC,QAAUD,kBAAkBpB,QAAQ,CAACqB,MAAM,GAE7D;gBACA;YACF;YACA,IAAI,CAACH,gBAAgB,GAAGlB;YAExB,4DAA4D;YAC5D,IAAI,CAACA,QAAQ,CAACsB,MAAM,GAAGtB,SAASuB,MAAM,CAAC,CAACd,UAAY,CAACA,QAAQe,SAAS;YAEtE,2EAA2E;YAC3E,MAAMC,UAAUzB,SAASuB,MAAM,CAAC,CAACd,UAAYA,QAAQe,SAAS;YAE9D,yEAAyE;YACzE,sEAAsE;YACtE,wEAAwE;YACxE,qEAAqE;YACrE,uBAAuB;YAEvB,MAAME,YAAY,IAAIpB;YACtB,MAAMqB,YAAY,IAAIC;YACtB,IAAK,IAAIP,QAAQ,GAAGA,QAAQI,QAAQjC,MAAM,EAAE6B,QAAS;gBACnD,yCAAyC;gBACzC,MAAMP,WAAWW,OAAO,CAACJ,MAAM,CAACR,UAAU,CAACC,QAAQ;gBAEnD,mEAAmE;gBACnE,MAAMe,UAAUH,UAAUd,GAAG,CAACE,aAAa,EAAE;gBAC7Ce,QAAQb,IAAI,CAACK;gBAEb,iEAAiE;gBACjE,mEAAmE;gBACnE,uDAAuD;gBACvD,IAAIQ,QAAQrC,MAAM,KAAK,GAAGkC,UAAUT,GAAG,CAACH,UAAUe;qBAE7C;gBAELF,UAAUX,IAAI,CAACF;YACjB;YAEA,+BAA+B;YAC/B,MAAMgB,SAASC,IAAAA,sBAAe,EAACJ;YAE/B,yEAAyE;YACzE,wEAAwE;YACxE,wEAAwE;YACxE,uEAAuE;YACvE,iBAAiB;YACjB,MAAMK,wBAA6C,EAAE;YACrD,KAAK,MAAMlB,YAAYgB,OAAQ;gBAC7B,MAAMD,UAAUH,UAAUd,GAAG,CAACE;gBAC9B,IAAI,CAACc,MAAMK,OAAO,CAACJ,UAAU;oBAC3B,MAAM,IAAIK,MAAM;gBAClB;gBAEA,MAAMC,iBAAiBN,QAAQzB,GAAG,CAAC,CAACiB,QAAUI,OAAO,CAACJ,MAAM;gBAE5DW,sBAAsBhB,IAAI,IAAImB;YAChC;YAEA,IAAI,CAACnC,QAAQ,CAACyB,OAAO,GAAGO;YAExB,uEAAuE;YACvE,IAAI,IAAI,CAAC1C,aAAa,KAAKA,eAAe;gBACxC,MAAM,IAAI4C,MACR;YAEJ;QACF,EAAE,OAAOE,KAAK;YACZtC,OAAOsC;QACT,SAAU;YACR,oEAAoE;YACpE,IAAI,CAACC,iBAAiB,GAAG/C;YACzBO;QACF;IACF;IAEOmB,KAAKX,QAA8B,EAAQ;QAChD,IAAI,CAACd,SAAS,CAACyB,IAAI,CAACX;IACtB;IAEA,MAAaiC,KAAKxB,QAAgB,EAAEyB,OAAqB,EAAoB;QAC3E,6CAA6C;QAC7C,MAAMC,QAAQ,MAAM,IAAI,CAACA,KAAK,CAAC1B,UAAUyB;QAEzC,0EAA0E;QAC1E,uEAAuE;QACvE,0BAA0B;QAC1B,OAAOC,UAAU;IACnB;IAEA,MAAaA,MACX1B,QAAgB,EAChByB,OAAqB,EACmC;QACxD,4EAA4E;QAC5E,yEAAyE;QACzE,mBAAmB;QACnB,WAAW,MAAMC,SAAS,IAAI,CAACC,QAAQ,CAAC3B,UAAUyB,SAAU;YAC1D,OAAOC;QACT;QAEA,OAAO;IACT;IAEA;;;;;;;GAOC,GACD,AAAUE,SACR5B,QAAgB,EAChBL,OAAqB,EACrB8B,OAAqB,EACF;YAQfA;QAPJ,IAAI9B,mBAAmBkC,sCAAkB,EAAE;YACzC,OAAOlC,QAAQ+B,KAAK,CAAC1B,UAAUyB;QACjC;QAEA,wEAAwE;QACxE,sEAAsE;QACtE,qDAAqD;QACrD,KAAIA,gBAAAA,QAAQK,IAAI,qBAAZL,cAAcM,mBAAmB,EAAE;YACrC,OAAOpC,QAAQ+B,KAAK,CAACD,QAAQK,IAAI,CAAC9B,QAAQ;QAC5C;QAEA,OAAOL,QAAQ+B,KAAK,CAAC1B;IACvB;IAEA,OAAc2B,SACZ3B,QAAgB,EAChByB,OAAqB,EACoD;QACzE,yEAAyE;QACzE,4EAA4E;QAC5E,2EAA2E;QAC3E,4EAA4E;QAC5E,4EAA4E;QAC5E,SAAS;QACT,IAAI,IAAI,CAACF,iBAAiB,KAAK,IAAI,CAAC/C,aAAa,EAAE;YACjD,MAAM,IAAI4C,MACR;QAEJ;QAEA,0DAA0D;QAC1DpB,WAAWgC,IAAAA,sCAAkB,EAAChC;QAE9B,2EAA2E;QAC3E,wEAAwE;QACxE,4EAA4E;QAC5E,sCAAsC;QACtC,IAAI,CAACiC,IAAAA,qBAAc,EAACjC,WAAW;YAC7B,KAAK,MAAML,WAAW,IAAI,CAACT,QAAQ,CAACsB,MAAM,CAAE;gBAC1C,MAAMkB,QAAQ,IAAI,CAACE,QAAQ,CAAC5B,UAAUL,SAAS8B;gBAC/C,IAAI,CAACC,OAAO;gBAEZ,MAAMA;YACR;QACF;QAEA,uDAAuD;QACvD,IAAID,2BAAAA,QAASS,WAAW,EAAE,OAAO;QAEjC,uDAAuD;QACvD,KAAK,MAAMvC,WAAW,IAAI,CAACT,QAAQ,CAACyB,OAAO,CAAE;YAC3C,MAAMe,QAAQ,IAAI,CAACE,QAAQ,CAAC5B,UAAUL,SAAS8B;YAC/C,IAAI,CAACC,OAAO;YAEZ,MAAMA;QACR;QAEA,4EAA4E;QAC5E,gCAAgC;QAChC,OAAO;IACT;;aA/QiBjD,YAAyC,EAAE;aACzCS,WAA0B;YAC3CsB,QAAQ,EAAE;YACVG,SAAS,EAAE;YACXlB,YAAY,CAAC;QACf;aACQ8B,oBAAoB,IAAI,CAAC/C,aAAa;aAkBtC4B,mBAAgD,EAAE;;AAwP5D"}