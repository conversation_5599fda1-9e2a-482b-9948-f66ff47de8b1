{"version": 3, "sources": ["../../../../../src/server/future/route-matcher-providers/dev/dev-app-page-route-matcher-provider.ts"], "names": ["DevAppPageRouteMatcherProvider", "FileCacheRouteMatcherProvider", "constructor", "appDir", "extensions", "reader", "normalizers", "DevAppNormalizers", "expression", "RegExp", "join", "transform", "files", "cache", "Map", "routeFilenames", "Array", "appPaths", "filename", "test", "page", "normalize", "includes", "push", "pathname", "bundlePath", "set", "normalizeCatchAllRoutes", "Object", "fromEntries", "entries", "map", "k", "v", "sort", "matchers", "cached", "get", "Error", "AppPageRouteMatcher", "kind", "RouteKind", "APP_PAGE"], "mappings": ";;;;+BAQaA;;;eAAAA;;;qCAPuB;2BACV;+CACoB;qBAEZ;yCACM;AAEjC,MAAMA,uCAAuCC,4DAA6B;IAI/EC,YACEC,MAAc,EACdC,UAAiC,EACjCC,MAAkB,CAClB;QACA,KAAK,CAACF,QAAQE;QAEd,IAAI,CAACC,WAAW,GAAG,IAAIC,sBAAiB,CAACJ,QAAQC;QAEjD,mGAAmG;QACnG,aAAa;QACb,IAAI,CAACI,UAAU,GAAG,IAAIC,OACpB,CAAC,2BAA2B,EAAEL,WAAWM,IAAI,CAAC,KAAK,EAAE,CAAC;IAE1D;IAEA,MAAgBC,UACdC,KAA4B,EACiB;QAC7C,2EAA2E;QAC3E,UAAU;QACV,MAAMC,QAAQ,IAAIC;QAIlB,MAAMC,iBAAiB,IAAIC;QAC3B,IAAIC,WAAqC,CAAC;QAC1C,KAAK,MAAMC,YAAYN,MAAO;YAC5B,4DAA4D;YAC5D,IAAI,CAAC,IAAI,CAACJ,UAAU,CAACW,IAAI,CAACD,WAAW;YAErC,MAAME,OAAO,IAAI,CAACd,WAAW,CAACc,IAAI,CAACC,SAAS,CAACH;YAE7C,6CAA6C;YAC7C,IAAIE,KAAKE,QAAQ,CAAC,OAAO;YAEzB,6DAA6D;YAC7DP,eAAeQ,IAAI,CAACL;YAEpB,MAAMM,WAAW,IAAI,CAAClB,WAAW,CAACkB,QAAQ,CAACH,SAAS,CAACH;YACrD,MAAMO,aAAa,IAAI,CAACnB,WAAW,CAACmB,UAAU,CAACJ,SAAS,CAACH;YAEzD,kCAAkC;YAClCL,MAAMa,GAAG,CAACR,UAAU;gBAAEE;gBAAMI;gBAAUC;YAAW;YAEjD,IAAID,YAAYP,UAAUA,QAAQ,CAACO,SAAS,CAACD,IAAI,CAACH;iBAC7CH,QAAQ,CAACO,SAAS,GAAG;gBAACJ;aAAK;QAClC;QAEAO,IAAAA,gDAAuB,EAACV;QAExB,sEAAsE;QACtEA,WAAWW,OAAOC,WAAW,CAC3BD,OAAOE,OAAO,CAACb,UAAUc,GAAG,CAAC,CAAC,CAACC,GAAGC,EAAE,GAAK;gBAACD;gBAAGC,EAAEC,IAAI;aAAG;QAGxD,MAAMC,WAAuC,EAAE;QAC/C,KAAK,MAAMjB,YAAYH,eAAgB;YACrC,6CAA6C;YAC7C,MAAMqB,SAASvB,MAAMwB,GAAG,CAACnB;YACzB,IAAI,CAACkB,QAAQ;gBACX,MAAM,IAAIE,MAAM;YAClB;YACA,MAAM,EAAEd,QAAQ,EAAEJ,IAAI,EAAEK,UAAU,EAAE,GAAGW;YAEvCD,SAASZ,IAAI,CACX,IAAIgB,wCAAmB,CAAC;gBACtBC,MAAMC,oBAAS,CAACC,QAAQ;gBACxBlB;gBACAJ;gBACAK;gBACAP;gBACAD,UAAUA,QAAQ,CAACO,SAAS;YAC9B;QAEJ;QACA,OAAOW;IACT;AACF"}