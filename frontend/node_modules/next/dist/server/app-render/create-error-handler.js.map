{"version": 3, "sources": ["../../../src/server/app-render/create-error-handler.tsx"], "names": ["ErrorHandlerSource", "createErrorHandler", "serverComponents", "flightData", "html", "source", "dev", "isNextExport", "errorLogger", "digestErrorsMap", "allCapturedErrors", "silenceLogger", "err", "errorInfo", "digest", "stringHash", "message", "stack", "toString", "push", "isDynamicUsageError", "isAbortError", "has", "set", "get", "formatServerError", "includes", "span", "getTracer", "getActiveScopeSpan", "recordException", "setStatus", "code", "SpanStatusCode", "ERROR", "catch", "__next_log_error__", "console", "error"], "mappings": ";;;;;;;;;;;;;;;IAeaA,kBAAkB;eAAlBA;;IAWGC,kBAAkB;eAAlBA;;;mEA1BO;mCACW;wBACQ;8BACb;qCACO;;;;;;AAW7B,MAAMD,qBAAqB;IAChCE,kBAAkB;IAClBC,YAAY;IACZC,MAAM;AACR;AAOO,SAASH,mBAAmB,EACjC;;GAEC,GACDI,MAAM,EACNC,GAAG,EACHC,YAAY,EACZC,WAAW,EACXC,eAAe,EACfC,iBAAiB,EACjBC,aAAa,EASd;IACC,OAAO,CAACC,KAAUC;YAsCZD;QArCJ,kEAAkE;QAClE,uDAAuD;QACvD,IAAI,CAACA,IAAIE,MAAM,EAAE;YACf,+EAA+E;YAC/EF,IAAIE,MAAM,GAAGC,IAAAA,mBAAU,EACrBH,IAAII,OAAO,GAAIH,CAAAA,CAAAA,6BAAAA,UAAWI,KAAK,KAAIL,IAAIK,KAAK,IAAI,EAAC,GACjDC,QAAQ;QACZ;QACA,MAAMJ,SAASF,IAAIE,MAAM;QAEzB,IAAIJ,mBAAmBA,kBAAkBS,IAAI,CAACP;QAE9C,kDAAkD;QAClD,wCAAwC;QACxC,IAAIQ,IAAAA,wCAAmB,EAACR,MAAM,OAAOA,IAAIE,MAAM;QAE/C,8DAA8D;QAC9D,IAAIO,IAAAA,0BAAY,EAACT,MAAM;QAEvB,IAAI,CAACH,gBAAgBa,GAAG,CAACR,SAAS;YAChCL,gBAAgBc,GAAG,CAACT,QAAQF;QAC9B,OAAO,IAAIP,WAAWL,mBAAmBI,IAAI,EAAE;YAC7C,gEAAgE;YAChE,yEAAyE;YACzEQ,MAAMH,gBAAgBe,GAAG,CAACV;QAC5B;QAEA,yEAAyE;QACzE,IAAIR,KAAK;YACPmB,IAAAA,oCAAiB,EAACb;QACpB;QACA,kCAAkC;QAClC,6BAA6B;QAC7B,+CAA+C;QAC/C,IACE,CACEL,CAAAA,iBACAK,wBAAAA,eAAAA,IAAKI,OAAO,qBAAZJ,aAAcc,QAAQ,CACpB,4FACF,GAEF;YACA,oDAAoD;YACpD,MAAMC,OAAOC,IAAAA,iBAAS,IAAGC,kBAAkB;YAC3C,IAAIF,MAAM;gBACRA,KAAKG,eAAe,CAAClB;gBACrBe,KAAKI,SAAS,CAAC;oBACbC,MAAMC,sBAAc,CAACC,KAAK;oBAC1BlB,SAASJ,IAAII,OAAO;gBACtB;YACF;YAEA,IAAI,CAACL,eAAe;gBAClB,IAAIH,aAAa;oBACfA,YAAYI,KAAKuB,KAAK,CAAC,KAAO;gBAChC,OAAO;oBACL,kEAAkE;oBAClE,gDAAgD;oBAChD,4DAA4D;oBAC5D,IAAI,OAAOC,uBAAuB,YAAY;wBAC5CA,mBAAmBxB;oBACrB,OAAO;wBACLyB,QAAQC,KAAK,CAAC1B;oBAChB;gBACF;YACF;QACF;QAEA,OAAOA,IAAIE,MAAM;IACnB;AACF"}