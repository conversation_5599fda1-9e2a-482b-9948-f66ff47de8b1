{"version": 3, "sources": ["../../../src/server/app-render/get-layer-assets.tsx"], "names": ["getLayerAssets", "ctx", "layoutOrPagePath", "injectedCSS", "injectedCSSWithCurrentLayout", "injectedJS", "injectedJSWithCurrentLayout", "injectedFontPreloadTags", "injectedFontPreloadTagsWithCurrentLayout", "styles", "styleTags", "scripts", "scriptTags", "getLinkAndScriptTags", "clientReferenceManifest", "preloadedFontFiles", "getPreloadableFonts", "renderOpts", "nextFontManifest", "length", "i", "fontFilename", "ext", "exec", "type", "href", "assetPrefix", "encodeURIPath", "componentMod", "preloadFont", "crossOrigin", "url", "URL", "preconnect", "origin", "error", "map", "index", "fullHref", "getAssetQueryString", "precedence", "process", "env", "NODE_ENV", "preloadStyle", "link", "rel", "fullSrc", "script", "src", "async"], "mappings": ";;;;+BAOgBA;;;eAAAA;;;;8DAPE;uCACmB;qCACD;qCAEA;+BACN;;;;;;AAEvB,SAASA,eAAe,EAC7BC,GAAG,EACHC,gBAAgB,EAChBC,aAAaC,4BAA4B,EACzCC,YAAYC,2BAA2B,EACvCC,yBAAyBC,wCAAwC,EAOlE;IACC,MAAM,EAAEC,QAAQC,SAAS,EAAEC,SAASC,UAAU,EAAE,GAAGV,mBAC/CW,IAAAA,2CAAoB,EAClBZ,IAAIa,uBAAuB,EAC3BZ,kBACAE,8BACAE,6BACA,QAEF;QAAEG,QAAQ,EAAE;QAAEE,SAAS,EAAE;IAAC;IAE9B,MAAMI,qBAAqBb,mBACvBc,IAAAA,wCAAmB,EACjBf,IAAIgB,UAAU,CAACC,gBAAgB,EAC/BhB,kBACAM,4CAEF;IAEJ,IAAIO,oBAAoB;QACtB,IAAIA,mBAAmBI,MAAM,EAAE;YAC7B,IAAK,IAAIC,IAAI,GAAGA,IAAIL,mBAAmBI,MAAM,EAAEC,IAAK;gBAClD,MAAMC,eAAeN,kBAAkB,CAACK,EAAE;gBAC1C,MAAME,MAAM,8BAA8BC,IAAI,CAACF,aAAc,CAAC,EAAE;gBAChE,MAAMG,OAAO,CAAC,KAAK,EAAEF,IAAI,CAAC;gBAC1B,MAAMG,OAAO,CAAC,EAAExB,IAAIyB,WAAW,CAAC,OAAO,EAAEC,IAAAA,4BAAa,EAACN,cAAc,CAAC;gBACtEpB,IAAI2B,YAAY,CAACC,WAAW,CAACJ,MAAMD,MAAMvB,IAAIgB,UAAU,CAACa,WAAW;YACrE;QACF,OAAO;YACL,IAAI;gBACF,IAAIC,MAAM,IAAIC,IAAI/B,IAAIyB,WAAW;gBACjCzB,IAAI2B,YAAY,CAACK,UAAU,CAACF,IAAIG,MAAM,EAAE;YAC1C,EAAE,OAAOC,OAAO;gBACd,mEAAmE;gBACnE,8CAA8C;gBAC9ClC,IAAI2B,YAAY,CAACK,UAAU,CAAC,KAAK;YACnC;QACF;IACF;IAEA,MAAMxB,SAASC,YACXA,UAAU0B,GAAG,CAAC,CAACX,MAAMY;QACnB,iEAAiE;QACjE,kDAAkD;QAClD,mDAAmD;QACnD,mEAAmE;QACnE,mEAAmE;QACnE,cAAc;QACd,MAAMC,WAAW,CAAC,EAAErC,IAAIyB,WAAW,CAAC,OAAO,EAAEC,IAAAA,4BAAa,EACxDF,MACA,EAAEc,IAAAA,wCAAmB,EAACtC,KAAK,MAAM,CAAC;QAEpC,gEAAgE;QAChE,oEAAoE;QACpE,2DAA2D;QAC3D,iEAAiE;QACjE,0DAA0D;QAC1D,+CAA+C;QAC/C,MAAMuC,aACJC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,UAAUlB,OAAO;QAE5DxB,IAAI2B,YAAY,CAACgB,YAAY,CAACN,UAAUrC,IAAIgB,UAAU,CAACa,WAAW;QAElE,qBACE,qBAACe;YACCC,KAAI;YACJrB,MAAMa;YACN,aAAa;YACbE,YAAYA;YACZV,aAAa7B,IAAIgB,UAAU,CAACa,WAAW;WAClCO;IAGX,KACA,EAAE;IAEN,MAAM1B,UAAUC,aACZA,WAAWwB,GAAG,CAAC,CAACX,MAAMY;QACpB,MAAMU,UAAU,CAAC,EAAE9C,IAAIyB,WAAW,CAAC,OAAO,EAAEC,IAAAA,4BAAa,EACvDF,MACA,EAAEc,IAAAA,wCAAmB,EAACtC,KAAK,MAAM,CAAC;QAEpC,qBAAO,qBAAC+C;YAAOC,KAAKF;YAASG,OAAO;WAAW,CAAC,OAAO,EAAEb,MAAM,CAAC;IAClE,KACA,EAAE;IAEN,OAAO5B,OAAOU,MAAM,IAAIR,QAAQQ,MAAM,GAAG;WAAIV;WAAWE;KAAQ,GAAG;AACrE"}