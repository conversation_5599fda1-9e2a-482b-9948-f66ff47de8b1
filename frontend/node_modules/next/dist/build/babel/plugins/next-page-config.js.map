{"version": 3, "sources": ["../../../../src/build/babel/plugins/next-page-config.ts"], "names": ["nextPageConfig", "CONFIG_KEY", "replaceBundle", "path", "t", "parentPath", "replaceWith", "program", "variableDeclaration", "variableDeclarator", "identifier", "STRING_LITERAL_DROP_BUNDLE", "stringLiteral", "Date", "now", "errorMessage", "state", "details", "pageName", "filename", "split", "cwd", "pop", "types", "visitor", "Program", "enter", "traverse", "ExportDeclaration", "exportPath", "exportState", "BabelTypes", "isExportNamedDeclaration", "node", "specifiers", "some", "specifier", "isIdentifier", "exported", "name", "value", "isStringLiteral", "source", "Error", "ExportNamedDeclaration", "bundleDropped", "declaration", "length", "config", "declarations", "scope", "getBinding", "filter", "Boolean", "local", "isImportSpecifier", "id", "init", "isTSAsExpression", "expression", "isObjectExpression", "got", "type", "prop", "properties", "isSpreadElement", "key", "isObjectProperty", "isBooleanLiteral", "amp", "file", "opts", "caller", "isDev"], "mappings": ";;;;+BAuCA,kDAAkD;AAClD;;;eAAwBA;;;sBAxCY;2BAQO;AAE3C,MAAMC,aAAa;AAEnB,qEAAqE;AACrE,SAASC,cAAcC,IAAS,EAAEC,CAAoB;IACpDD,KAAKE,UAAU,CAACC,WAAW,CACzBF,EAAEG,OAAO,CACP;QACEH,EAAEI,mBAAmB,CAAC,SAAS;YAC7BJ,EAAEK,kBAAkB,CAClBL,EAAEM,UAAU,CAACC,qCAA0B,GACvCP,EAAEQ,aAAa,CAAC,CAAC,EAAED,qCAA0B,CAAC,CAAC,EAAEE,KAAKC,GAAG,GAAG,CAAC;SAEhE;KACF,EACD,EAAE;AAGR;AAEA,SAASC,aAAaC,KAAU,EAAEC,OAAe;IAC/C,MAAMC,WACJ,AAACF,CAAAA,MAAMG,QAAQ,IAAI,EAAC,EAAGC,KAAK,CAACJ,MAAMK,GAAG,IAAI,IAAIC,GAAG,MAAM;IACzD,OAAO,CAAC,kCAAkC,EAAEL,QAAQ,SAAS,EAAEC,SAAS,2DAA2D,CAAC;AACtI;AAOe,SAASlB,eAAe,EACrCuB,OAAOnB,CAAC,EAGT;IACC,OAAO;QACLoB,SAAS;YACPC,SAAS;gBACPC,OAAMvB,IAAI,EAAEa,KAAK;oBACfb,KAAKwB,QAAQ,CACX;wBACEC,mBAAkBC,UAAU,EAAEC,WAAW;gCAGrCD;4BAFF,IACEE,WAAU,CAACC,wBAAwB,CAACH,WAAWI,IAAI,OACnDJ,8BAAAA,WAAWI,IAAI,CAACC,UAAU,qBAA1BL,4BAA4BM,IAAI,CAAC,CAACC;gCAChC,OACE,AAAChC,CAAAA,EAAEiC,YAAY,CAACD,UAAUE,QAAQ,IAC9BF,UAAUE,QAAQ,CAACC,IAAI,GACvBH,UAAUE,QAAQ,CAACE,KAAK,AAAD,MAAOvC;4BAEtC,OACA8B,WAAU,CAACU,eAAe,CACxB,AAACZ,WAAWI,IAAI,CACbS,MAAM,GAEX;gCACA,MAAM,IAAIC,MACR5B,aACEe,aACA;4BAGN;wBACF;wBACAc,wBACEf,UAAuD,EACvDC,WAAgB;gCAaZD,8BAGFA;4BAdF,IACEC,YAAYe,aAAa,IACxB,CAAChB,WAAWI,IAAI,CAACa,WAAW,IAC3BjB,WAAWI,IAAI,CAACC,UAAU,CAACa,MAAM,KAAK,GACxC;gCACA;4BACF;4BAEA,MAAMC,SAAqB,CAAC;4BAC5B,MAAMC,eAAgD;mCAChD,EACFpB,+BAAAA,WAAWI,IAAI,CACZa,WAAW,qBAFZ,AACFjB,6BAECoB,YAAY,KAAI,EAAE;iCACrBpB,+BAAAA,WAAWqB,KAAK,CAACC,UAAU,CAAClD,gCAA5B4B,6BAAyC1B,IAAI,CAC1C8B,IAAI;6BACR,CAACmB,MAAM,CAACC;4BAET,KAAK,MAAMjB,aAAaP,WAAWI,IAAI,CAACC,UAAU,CAAE;gCAClD,IACE,AAAC9B,CAAAA,EAAEiC,YAAY,CAACD,UAAUE,QAAQ,IAC9BF,UAAUE,QAAQ,CAACC,IAAI,GACvBH,UAAUE,QAAQ,CAACE,KAAK,AAAD,MAAOvC,YAClC;oCACA,6BAA6B;oCAC7B,IAAI8B,WAAU,CAACU,eAAe,CAACZ,WAAWI,IAAI,CAACS,MAAM,GAAG;wCACtD,MAAM,IAAIC,MACR5B,aACEe,aACA,CAAC,8BAA8B,CAAC;oCAGpC,4BAA4B;oCAC5B,6BAA6B;oCAC/B,OAAO,IACLC,WAAU,CAACM,YAAY,CACrB,AAACD,UAAyCkB,KAAK,GAEjD;4CAGIzB;wCAFJ,IACEE,WAAU,CAACwB,iBAAiB,EAC1B1B,gCAAAA,WAAWqB,KAAK,CAACC,UAAU,CACzB,AAACf,UAAyCkB,KAAK,CAACf,IAAI,sBADtDV,8BAEG1B,IAAI,CAAC8B,IAAI,GAEd;4CACA,MAAM,IAAIU,MACR5B,aACEe,aACA,CAAC,8BAA8B,CAAC;wCAGtC;oCACF;gCACF;4BACF;4BAEA,KAAK,MAAMgB,eAAeG,aAAc;gCACtC,IACE,CAAClB,WAAU,CAACM,YAAY,CAACS,YAAYU,EAAE,EAAE;oCACvCjB,MAAMtC;gCACR,IACA;oCACA;gCACF;gCAEA,IAAI,EAAEwD,IAAI,EAAE,GAAGX;gCACf,IAAIf,WAAU,CAAC2B,gBAAgB,CAACD,OAAO;oCACrCA,OAAOA,KAAKE,UAAU;gCACxB;gCAEA,IAAI,CAAC5B,WAAU,CAAC6B,kBAAkB,CAACH,OAAO;oCACxC,MAAMI,MAAMJ,OAAOA,KAAKK,IAAI,GAAG;oCAC/B,MAAM,IAAInB,MACR5B,aACEe,aACA,CAAC,wBAAwB,EAAE+B,IAAI,CAAC;gCAGtC;gCAEA,KAAK,MAAME,QAAQN,KAAKO,UAAU,CAAE;oCAClC,IAAIjC,WAAU,CAACkC,eAAe,CAACF,OAAO;wCACpC,MAAM,IAAIpB,MACR5B,aACEe,aACA,CAAC,8BAA8B,CAAC;oCAGtC;oCACA,MAAM,EAAES,IAAI,EAAE,GAAGwB,KAAKG,GAAG;oCACzB,IAAInC,WAAU,CAACM,YAAY,CAAC0B,KAAKG,GAAG,EAAE;wCAAE3B,MAAM;oCAAM,IAAI;wCACtD,IAAI,CAACR,WAAU,CAACoC,gBAAgB,CAACJ,OAAO;4CACtC,MAAM,IAAIpB,MACR5B,aACEe,aACA,CAAC,kBAAkB,EAAES,KAAK,CAAC,CAAC;wCAGlC;wCACA,IACE,CAACR,WAAU,CAACqC,gBAAgB,CAACL,KAAKvB,KAAK,KACvC,CAACT,WAAU,CAACU,eAAe,CAACsB,KAAKvB,KAAK,GACtC;4CACA,MAAM,IAAIG,MACR5B,aACEe,aACA,CAAC,mBAAmB,EAAES,KAAK,CAAC,CAAC;wCAGnC;wCACAS,OAAOqB,GAAG,GAAGN,KAAKvB,KAAK,CAACA,KAAK;oCAC/B;gCACF;4BACF;4BAEA,IAAIQ,OAAOqB,GAAG,KAAK,MAAM;oCAClBvC,wBAAAA;gCAAL,IAAI,GAACA,oBAAAA,YAAYwC,IAAI,sBAAhBxC,yBAAAA,kBAAkByC,IAAI,qBAAtBzC,uBAAwB0C,MAAM,CAACC,KAAK,GAAE;oCACzC,uDAAuD;oCACvD,wDAAwD;oCACxDvE,cAAc2B,YAAYzB;gCAC5B;gCACA0B,YAAYe,aAAa,GAAG;gCAC5B;4BACF;wBACF;oBACF,GACA7B;gBAEJ;YACF;QACF;IACF;AACF"}