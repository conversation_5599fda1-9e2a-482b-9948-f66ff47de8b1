#!/bin/bash

# ML Experiment Platform - Cleanup Script

set -e

echo "🧹 Cleaning up ML Experiment Platform development environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Parse command line arguments
FULL_CLEANUP=0
KEEP_DATA=1

while [[ $# -gt 0 ]]; do
    case $1 in
        --full)
            FULL_CLEANUP=1
            shift
            ;;
        --remove-data)
            KEEP_DATA=0
            shift
            ;;
        --help)
            echo "Usage: $0 [options]"
            echo "Options:"
            echo "  --full         Remove all containers, images, and volumes"
            echo "  --remove-data  Remove persistent data volumes (databases, etc.)"
            echo "  --help         Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Stop all services
echo "🛑 Stopping all services..."
if docker compose -f docker-compose.dev.yml ps -q | grep -q .; then
    docker compose -f docker-compose.dev.yml down
    print_status "Services stopped"
else
    print_warning "No services were running"
fi

# Remove containers
if [ $FULL_CLEANUP -eq 1 ]; then
    echo "🗑️  Removing containers..."
    docker compose -f docker-compose.dev.yml down --remove-orphans
    print_status "Containers removed"
fi

# Remove volumes (data)
if [ $KEEP_DATA -eq 0 ]; then
    echo "💾 Removing data volumes..."
    docker compose -f docker-compose.dev.yml down -v
    print_status "Data volumes removed"
    print_warning "All database data and uploaded files have been deleted!"
fi

# Remove images
if [ $FULL_CLEANUP -eq 1 ]; then
    echo "🖼️  Removing Docker images..."
    
    # Get image names from docker-compose
    IMAGES=$(docker compose -f docker-compose.dev.yml config | grep 'image:' | awk '{print $2}' | sort | uniq)
    
    for image in $IMAGES; do
        if docker images -q "$image" > /dev/null 2>&1; then
            docker rmi "$image" || print_warning "Could not remove image: $image"
        fi
    done
    
    # Remove custom built images
    docker images | grep "pathfinder-ml" | awk '{print $3}' | xargs -r docker rmi || true
    
    print_status "Docker images removed"
fi

# Clean up Python cache and virtual environments
echo "🐍 Cleaning Python artifacts..."
find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
find . -type f -name "*.pyc" -delete 2>/dev/null || true
find . -type f -name "*.pyo" -delete 2>/dev/null || true
find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true

if [ -d "backend/venv" ]; then
    rm -rf backend/venv
    print_status "Python virtual environment removed"
fi

# Clean up Node.js artifacts
echo "⚛️  Cleaning Node.js artifacts..."
if [ -d "frontend/node_modules" ]; then
    rm -rf frontend/node_modules
    print_status "Node.js modules removed"
fi

if [ -d "frontend/.next" ]; then
    rm -rf frontend/.next
    print_status "Next.js build cache removed"
fi

# Clean up logs
echo "📝 Cleaning log files..."
find . -name "*.log" -type f -delete 2>/dev/null || true
print_status "Log files removed"

# Clean up temporary files
echo "🗂️  Cleaning temporary files..."
find . -name ".DS_Store" -type f -delete 2>/dev/null || true
find . -name "Thumbs.db" -type f -delete 2>/dev/null || true
find . -name "*.tmp" -type f -delete 2>/dev/null || true
print_status "Temporary files removed"

# Docker system cleanup
if [ $FULL_CLEANUP -eq 1 ]; then
    echo "🧽 Running Docker system cleanup..."
    docker system prune -f
    print_status "Docker system cleaned"
fi

echo ""
print_status "Cleanup completed! 🎉"

if [ $FULL_CLEANUP -eq 1 ]; then
    echo ""
    print_warning "Full cleanup performed. You'll need to rebuild images on next startup."
fi

if [ $KEEP_DATA -eq 0 ]; then
    echo ""
    print_warning "Data volumes were removed. All databases and uploaded files are gone."
fi

echo ""
echo "To restart the development environment, run:"
echo "  ./scripts/dev-setup.sh"
