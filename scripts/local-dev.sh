#!/bin/bash

# ML Experiment Platform - Local Development Setup
# This script runs services locally without <PERSON><PERSON> to bypass networking issues

set -e

echo "🚀 Setting up ML Experiment Platform for local development..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if required tools are installed
check_requirements() {
    echo "🔍 Checking requirements..."
    
    # Check Python
    if ! command -v python3 >/dev/null 2>&1; then
        print_error "Python 3 is required but not installed"
        exit 1
    fi
    print_status "Python 3 found: $(python3 --version)"
    
    # Check Node.js
    if ! command -v node >/dev/null 2>&1; then
        print_error "Node.js is required but not installed"
        exit 1
    fi
    print_status "Node.js found: $(node --version)"
    
    # Check if PostgreSQL is available
    if command -v psql >/dev/null 2>&1; then
        print_status "PostgreSQL client found"
    else
        print_warning "PostgreSQL client not found - you'll need to install PostgreSQL"
        print_info "Install with: sudo apt install postgresql postgresql-contrib"
    fi
    
    # Check if Redis is available
    if command -v redis-server >/dev/null 2>&1; then
        print_status "Redis found"
    else
        print_warning "Redis not found - you'll need to install Redis"
        print_info "Install with: sudo apt install redis-server"
    fi
}

# Install system dependencies
install_system_deps() {
    echo ""
    echo "📦 Installing system dependencies..."
    
    # Update package list
    sudo apt update
    
    # Install PostgreSQL
    if ! command -v psql >/dev/null 2>&1; then
        echo "Installing PostgreSQL..."
        sudo apt install -y postgresql postgresql-contrib
        sudo systemctl start postgresql
        sudo systemctl enable postgresql
        print_status "PostgreSQL installed and started"
    fi
    
    # Install Redis
    if ! command -v redis-server >/dev/null 2>&1; then
        echo "Installing Redis..."
        sudo apt install -y redis-server
        sudo systemctl start redis-server
        sudo systemctl enable redis-server
        print_status "Redis installed and started"
    fi
    
    # Install MinIO (optional - can use file storage instead)
    if ! command -v minio >/dev/null 2>&1; then
        echo "Installing MinIO..."
        wget https://dl.min.io/server/minio/release/linux-amd64/minio -O /tmp/minio
        chmod +x /tmp/minio
        sudo mv /tmp/minio /usr/local/bin/
        print_status "MinIO installed"
    fi
}

# Set up PostgreSQL databases
setup_databases() {
    echo ""
    echo "🗄️ Setting up databases..."
    
    # Create databases and users
    sudo -u postgres psql -c "CREATE DATABASE ml_platform;" 2>/dev/null || print_warning "ml_platform database already exists"
    sudo -u postgres psql -c "CREATE DATABASE mlflow;" 2>/dev/null || print_warning "mlflow database already exists"
    sudo -u postgres psql -c "CREATE USER mlflow WITH PASSWORD 'mlflow_password';" 2>/dev/null || print_warning "mlflow user already exists"
    sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE mlflow TO mlflow;" 2>/dev/null || true
    
    print_status "Databases configured"
}

# Start background services
start_services() {
    echo ""
    echo "🔧 Starting background services..."
    
    # Start Redis if not running
    if ! pgrep redis-server >/dev/null; then
        sudo systemctl start redis-server
        print_status "Redis started"
    else
        print_status "Redis already running"
    fi
    
    # Start PostgreSQL if not running
    if ! pgrep postgres >/dev/null; then
        sudo systemctl start postgresql
        print_status "PostgreSQL started"
    else
        print_status "PostgreSQL already running"
    fi
    
    # Start MinIO in background (using local directory)
    if ! pgrep minio >/dev/null; then
        mkdir -p ./data/minio
        MINIO_ROOT_USER=minioadmin MINIO_ROOT_PASSWORD=minioadmin minio server ./data/minio --console-address ":9001" &
        echo $! > ./data/minio.pid
        print_status "MinIO started on ports 9000 (API) and 9001 (Console)"
    else
        print_status "MinIO already running"
    fi
    
    # Wait for services to be ready
    sleep 3
}

# Set up Python environment and install dependencies
setup_backend() {
    echo ""
    echo "🐍 Setting up backend..."
    
    cd backend
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        print_status "Python virtual environment created"
    fi
    
    # Activate virtual environment and install dependencies
    source venv/bin/activate
    pip install --upgrade pip
    pip install -r requirements.txt -r requirements-dev.txt
    print_status "Backend dependencies installed"
    
    cd ..
}

# Set up frontend
setup_frontend() {
    echo ""
    echo "⚛️  Setting up frontend..."
    
    cd frontend
    
    # Install dependencies
    npm install
    print_status "Frontend dependencies installed"
    
    cd ..
}

# Create environment file
create_env() {
    echo ""
    echo "📝 Creating environment configuration..."
    
    if [ ! -f .env ]; then
        cp .env.example .env
        
        # Update .env for local development
        sed -i 's|DATABASE_URL=.*|DATABASE_URL=postgresql://postgres:postgres@localhost:5432/ml_platform|' .env
        sed -i 's|REDIS_URL=.*|REDIS_URL=redis://localhost:6379/0|' .env
        sed -i 's|MINIO_ENDPOINT=.*|MINIO_ENDPOINT=localhost:9000|' .env
        sed -i 's|MLFLOW_TRACKING_URI=.*|MLFLOW_TRACKING_URI=http://localhost:5000|' .env
        
        print_status "Environment file created and configured for local development"
    else
        print_status "Environment file already exists"
    fi
}

# Start MLflow server
start_mlflow() {
    echo ""
    echo "📊 Starting MLflow server..."
    
    cd backend
    source venv/bin/activate
    
    # Start MLflow in background
    mlflow server \
        --backend-store-uri postgresql+psycopg2://mlflow:mlflow_password@localhost:5432/mlflow \
        --default-artifact-root ./data/mlflow-artifacts \
        --host 0.0.0.0 \
        --port 5000 &
    
    echo $! > ../data/mlflow.pid
    print_status "MLflow server started on http://localhost:5000"
    
    cd ..
}

# Main execution
main() {
    # Create data directory
    mkdir -p data
    
    # Check requirements
    check_requirements
    
    # Ask user if they want to install system dependencies
    echo ""
    read -p "Do you want to install system dependencies (PostgreSQL, Redis, MinIO)? [y/N]: " install_deps
    if [[ $install_deps =~ ^[Yy]$ ]]; then
        install_system_deps
        setup_databases
    fi
    
    # Start services
    start_services
    
    # Set up application
    setup_backend
    setup_frontend
    create_env
    start_mlflow
    
    echo ""
    echo "🎉 Local development environment is ready!"
    echo ""
    echo "📱 To start the application:"
    echo "   Backend:  cd backend && source venv/bin/activate && uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"
    echo "   Frontend: cd frontend && npm run dev"
    echo "   Agents:   cd agents && python -m coordinator.main"
    echo ""
    echo "🌐 Access URLs:"
    echo "   Frontend:     http://localhost:3000"
    echo "   Backend API:  http://localhost:8000"
    echo "   API Docs:     http://localhost:8000/docs"
    echo "   MLflow:       http://localhost:5000"
    echo "   MinIO:        http://localhost:9001 (minioadmin/minioadmin)"
    echo ""
    echo "🛑 To stop services:"
    echo "   ./scripts/stop-local.sh"
}

# Run main function
main "$@"
