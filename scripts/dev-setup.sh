#!/bin/bash

# ML Experiment Platform - Development Setup Script

set -e

echo "🚀 Setting up ML Experiment Platform for development..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ .env file created. Please review and update the configuration as needed."
fi

# Start infrastructure services first
echo "🏗️ Starting infrastructure services..."
docker-compose -f docker-compose.dev.yml up -d postgres redis minio

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 15

# Check if PostgreSQL is ready
echo "🔍 Checking PostgreSQL connection..."
until docker-compose -f docker-compose.dev.yml exec -T postgres pg_isready -U postgres; do
    echo "Waiting for PostgreSQL..."
    sleep 2
done

# Check if Redis is ready
echo "🔍 Checking Redis connection..."
until docker-compose -f docker-compose.dev.yml exec -T redis redis-cli ping; do
    echo "Waiting for Redis..."
    sleep 2
done

# Start MLflow
echo "📊 Starting MLflow server..."
docker-compose -f docker-compose.dev.yml up -d mlflow

# Wait for MLflow to be ready
echo "⏳ Waiting for MLflow to be ready..."
sleep 10

# Create MinIO buckets
echo "🪣 Setting up MinIO buckets..."
docker-compose -f docker-compose.dev.yml exec -T minio mc alias set myminio http://localhost:9000 minioadmin minioadmin
docker-compose -f docker-compose.dev.yml exec -T minio mc mb myminio/ml-platform-data --ignore-existing
docker-compose -f docker-compose.dev.yml exec -T minio mc mb myminio/mlflow-artifacts --ignore-existing

# Start backend API
echo "🔧 Starting backend API..."
docker-compose -f docker-compose.dev.yml up -d api

# Start frontend
echo "🎨 Starting frontend..."
docker-compose -f docker-compose.dev.yml up -d frontend

# Start AI agents
echo "🤖 Starting AI agents..."
docker-compose -f docker-compose.dev.yml up -d agents

echo ""
echo "🎉 Development environment is ready!"
echo ""
echo "📱 Access the application:"
echo "   Frontend:     http://localhost:3000"
echo "   Backend API:  http://localhost:8000"
echo "   API Docs:     http://localhost:8000/docs"
echo "   MLflow:       http://localhost:5000"
echo "   MinIO:        http://localhost:9001 (admin/admin)"
echo ""
echo "🔧 Useful commands:"
echo "   View logs:    docker-compose -f docker-compose.dev.yml logs -f [service]"
echo "   Stop all:     docker-compose -f docker-compose.dev.yml down"
echo "   Restart:      docker-compose -f docker-compose.dev.yml restart [service]"
echo ""
echo "📚 Next steps:"
echo "   1. Review and update .env configuration"
echo "   2. Run database migrations (when implemented)"
echo "   3. Create your first project!"
