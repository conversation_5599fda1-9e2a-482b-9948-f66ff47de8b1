#!/bin/bash

# ML Experiment Platform - Test Runner Script

set -e

echo "🧪 Running ML Experiment Platform test suite..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if services are running
check_services() {
    echo "🔍 Checking if development services are running..."
    
    if ! docker-compose -f docker-compose.dev.yml ps | grep -q "Up"; then
        print_warning "Development services are not running. Starting them..."
        docker-compose -f docker-compose.dev.yml up -d
        sleep 10
    else
        print_status "Development services are running"
    fi
}

# Run backend tests
run_backend_tests() {
    echo ""
    echo "🐍 Running backend tests..."
    
    if [ -d "backend" ]; then
        cd backend
        
        # Check if virtual environment exists
        if [ ! -d "venv" ]; then
            print_warning "Creating Python virtual environment..."
            python3 -m venv venv
        fi
        
        # Activate virtual environment
        source venv/bin/activate
        
        # Install dependencies
        pip install -r requirements.txt
        
        # Run linting
        echo "🔍 Running Python linting..."
        flake8 app/ --count --select=E9,F63,F7,F82 --show-source --statistics || true
        
        # Run type checking
        echo "🔍 Running Python type checking..."
        mypy app/ || true
        
        # Run tests
        echo "🧪 Running Python tests..."
        if pytest tests/ -v --cov=app --cov-report=term-missing; then
            print_status "Backend tests passed"
        else
            print_error "Backend tests failed"
            BACKEND_FAILED=1
        fi
        
        deactivate
        cd ..
    else
        print_warning "Backend directory not found, skipping backend tests"
    fi
}

# Run frontend tests
run_frontend_tests() {
    echo ""
    echo "⚛️  Running frontend tests..."
    
    if [ -d "frontend" ]; then
        cd frontend
        
        # Install dependencies if node_modules doesn't exist
        if [ ! -d "node_modules" ]; then
            print_warning "Installing Node.js dependencies..."
            npm install
        fi
        
        # Run linting
        echo "🔍 Running frontend linting..."
        npm run lint || true
        
        # Run type checking
        echo "🔍 Running TypeScript type checking..."
        npm run type-check || true
        
        # Run tests
        echo "🧪 Running frontend tests..."
        if npm test -- --watchAll=false; then
            print_status "Frontend tests passed"
        else
            print_error "Frontend tests failed"
            FRONTEND_FAILED=1
        fi
        
        cd ..
    else
        print_warning "Frontend directory not found, skipping frontend tests"
    fi
}

# Run integration tests
run_integration_tests() {
    echo ""
    echo "🔗 Running integration tests..."
    
    if [ -d "tests" ]; then
        # TODO: Implement integration tests
        print_warning "Integration tests not yet implemented"
    else
        print_warning "Integration tests directory not found"
    fi
}

# Main execution
main() {
    BACKEND_FAILED=0
    FRONTEND_FAILED=0
    
    # Parse command line arguments
    RUN_BACKEND=1
    RUN_FRONTEND=1
    RUN_INTEGRATION=1
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --backend-only)
                RUN_FRONTEND=0
                RUN_INTEGRATION=0
                shift
                ;;
            --frontend-only)
                RUN_BACKEND=0
                RUN_INTEGRATION=0
                shift
                ;;
            --no-integration)
                RUN_INTEGRATION=0
                shift
                ;;
            --help)
                echo "Usage: $0 [options]"
                echo "Options:"
                echo "  --backend-only     Run only backend tests"
                echo "  --frontend-only    Run only frontend tests"
                echo "  --no-integration   Skip integration tests"
                echo "  --help            Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Check services
    check_services
    
    # Run tests based on flags
    if [ $RUN_BACKEND -eq 1 ]; then
        run_backend_tests
    fi
    
    if [ $RUN_FRONTEND -eq 1 ]; then
        run_frontend_tests
    fi
    
    if [ $RUN_INTEGRATION -eq 1 ]; then
        run_integration_tests
    fi
    
    # Summary
    echo ""
    echo "📊 Test Summary:"
    
    if [ $RUN_BACKEND -eq 1 ]; then
        if [ $BACKEND_FAILED -eq 0 ]; then
            print_status "Backend tests: PASSED"
        else
            print_error "Backend tests: FAILED"
        fi
    fi
    
    if [ $RUN_FRONTEND -eq 1 ]; then
        if [ $FRONTEND_FAILED -eq 0 ]; then
            print_status "Frontend tests: PASSED"
        else
            print_error "Frontend tests: FAILED"
        fi
    fi
    
    # Exit with error if any tests failed
    if [ $BACKEND_FAILED -eq 1 ] || [ $FRONTEND_FAILED -eq 1 ]; then
        echo ""
        print_error "Some tests failed. Please check the output above."
        exit 1
    else
        echo ""
        print_status "All tests passed! 🎉"
    fi
}

# Run main function
main "$@"
