import pandas as pd
import numpy as np
from sklearn.datasets import make_regression
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error

from ray import tune, init, shutdown
from ray.tune.schedulers import HyperBandForBOHB
from ray.tune.suggest.bohb import TuneBOHB
from ray.tune.search import ConcurrencyLimiter
import matplotlib.pyplot as plt

# Initialize Ray
init(ignore_reinit_error=True)

# Generate synthetic regression dataset
X, y = make_regression(n_samples=2000, n_features=50, noise=0.1, random_state=42)
X_train_full, X_test, y_train_full, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# Save to disk for Ray workers
X_df = pd.DataFrame(X_train_full, columns=[f"x{i}" for i in range(1, 51)])
y_df = pd.DataFrame(y_train_full, columns=["target"])
X_df.to_csv("X_train.csv", index=False)
y_df.to_csv("y_train.csv", index=False)

def train_rf(config):
    X_train = pd.read_csv("X_train.csv")
    y_train = pd.read_csv("y_train.csv").values.ravel()
    X_train_split, X_val_split, y_train_split, y_val_split = train_test_split(X_train, y_train, test_size=0.2)

    model = RandomForestRegressor(
        n_estimators=int(config["n_estimators"]),
        max_depth=int(config["max_depth"]),
        min_samples_split=int(config["min_samples_split"]),
        min_samples_leaf=int(config["min_samples_leaf"])
    )

    model.fit(X_train_split, y_train_split)
    preds = model.predict(X_val_split)
    rmse = np.sqrt(mean_squared_error(y_val_split, preds))
    tune.report(rmse=rmse)

# Hyperparameter search space
search_space = {
    "n_estimators": tune.randint(50, 300),
    "max_depth": tune.randint(5, 30),
    "min_samples_split": tune.randint(2, 20),
    "min_samples_leaf": tune.randint(1, 10)
}

# BOHB setup
bohb_search = TuneBOHB()
bohb_search = ConcurrencyLimiter(bohb_search, max_concurrent=10)
scheduler = HyperBandForBOHB(time_attr="training_iteration", max_t=10)

# Run the tuning process
tuner = tune.Tuner(
    train_rf,
    tune_config=tune.TuneConfig(
        metric="rmse",
        mode="min",
        search_alg=bohb_search,
        scheduler=scheduler,
        num_samples=1000,
    ),
    param_space=search_space,
)

results = tuner.fit()

# Collect results into a DataFrame
df_results = results.get_dataframe()
df_results_sorted = df_results.sort_values(by="rmse")

# Shutdown Ray
shutdown()

# Visualize RMSE distribution
plt.figure(figsize=(10, 6))
plt.hist(df_results["rmse"], bins=30, edgecolor='black')
plt.title("Distribution of RMSE from 1000 BOHB Trials (Random Forest)")
plt.xlabel("RMSE")
plt.ylabel("Frequency")
plt.grid(True)
plt.tight_layout()
plt.show()

# Show the top 5 configurations
import ace_tools as tools; tools.display_dataframe_to_user(name="Top 5 BOHB Random Forest Configs", dataframe=df_results_sorted.head())
