#!/bin/bash

# ML Experiment Platform - Stop Local Services

echo "🛑 Stopping local development services..."

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Stop MLflow
if [ -f data/mlflow.pid ]; then
    PID=$(cat data/mlflow.pid)
    if kill -0 $PID 2>/dev/null; then
        kill $PID
        rm data/mlflow.pid
        print_status "MLflow server stopped"
    else
        print_warning "MLflow server was not running"
        rm data/mlflow.pid
    fi
else
    print_warning "MLflow PID file not found"
fi

# Stop MinIO
if [ -f data/minio.pid ]; then
    PID=$(cat data/minio.pid)
    if kill -0 $PID 2>/dev/null; then
        kill $PID
        rm data/minio.pid
        print_status "MinIO server stopped"
    else
        print_warning "MinIO server was not running"
        rm data/minio.pid
    fi
else
    print_warning "MinIO PID file not found"
fi

# Stop any running development servers
pkill -f "uvicorn app.main:app" 2>/dev/null && print_status "Backend server stopped" || true
pkill -f "npm run dev" 2>/dev/null && print_status "Frontend server stopped" || true
pkill -f "coordinator.main" 2>/dev/null && print_status "AI agents stopped" || true

echo ""
print_status "All local services stopped"
